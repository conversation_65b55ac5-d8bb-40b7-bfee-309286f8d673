#!/usr/bin/env python3
"""
Debug script to check what routes are actually registered in the proxy server.
"""

import sys
import os
import requests

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_proxy_routes():
    """Test what routes are available on the proxy server."""
    print("OAPIE-Lite Proxy Route Debug")
    print("=" * 40)
    
    base_url = "http://127.0.0.1:8000"
    
    # Test 1: Health check
    print("1. Testing health endpoint...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"   ✓ /health: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"   ✗ /health: {e}")
        print("   Proxy server is not running!")
        return False
    
    # Test 2: GUI relay endpoint
    print("\n2. Testing GUI relay endpoint...")
    try:
        test_payload = {"model": "gpt-3.5-turbo", "messages": [{"role": "user", "content": "test"}]}
        response = requests.post(f"{base_url}/v1/gui_relay", json=test_payload, timeout=5)
        print(f"   ✓ /v1/gui_relay: {response.status_code}")
    except Exception as e:
        print(f"   ✗ /v1/gui_relay: {e}")
    
    # Test 3: The problematic chat completions endpoint
    print("\n3. Testing chat completions endpoint...")
    try:
        test_payload = {"model": "gpt-3.5-turbo", "messages": [{"role": "user", "content": "test"}]}
        response = requests.post(f"{base_url}/v1/chat/completions", json=test_payload, timeout=5)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text[:200]}...")
    except Exception as e:
        print(f"   ✗ /v1/chat/completions: {e}")
    
    # Test 4: Check if the route pattern works with other paths
    print("\n4. Testing other v1 paths...")
    test_paths = ["v1/models", "v1/completions", "v1/test"]
    for path in test_paths:
        try:
            response = requests.post(f"{base_url}/{path}", json={"test": "data"}, timeout=5)
            print(f"   /{path}: {response.status_code}")
        except Exception as e:
            print(f"   ✗ /{path}: {e}")
    
    # Test 5: Try to get route information from FastAPI
    print("\n5. Checking FastAPI route registration...")
    try:
        from src.proxy_server import app
        print("   Registered routes:")
        for route in app.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                print(f"   - {route.methods} {route.path}")
    except Exception as e:
        print(f"   ✗ Error checking routes: {e}")
    
    return True

def test_direct_import():
    """Test importing and running the proxy server directly."""
    print("\n" + "=" * 40)
    print("DIRECT IMPORT TEST")
    print("=" * 40)
    
    try:
        from src.proxy_server import app
        print("✓ Successfully imported proxy server app")
        
        # Check if the app has the expected routes
        routes = []
        for route in app.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                routes.append(f"{list(route.methods)} {route.path}")
        
        print(f"✓ Found {len(routes)} routes:")
        for route in routes:
            print(f"   {route}")
            
        # Check specifically for our v1 route
        v1_routes = [r for r in routes if '/v1/' in r]
        if v1_routes:
            print(f"✓ Found v1 routes: {v1_routes}")
        else:
            print("✗ No v1 routes found!")
            
    except Exception as e:
        print(f"✗ Error importing proxy server: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main function."""
    print("Starting proxy server route debugging...\n")
    
    # First test direct import
    test_direct_import()
    
    # Then test actual HTTP requests
    test_proxy_routes()
    
    print("\n" + "=" * 40)
    print("DEBUG COMPLETE")
    print("=" * 40)

if __name__ == "__main__":
    main()
