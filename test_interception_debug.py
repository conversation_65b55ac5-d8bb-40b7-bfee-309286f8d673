#!/usr/bin/env python3
"""
Debug script to test the interception functionality of OAPIE-Lite.

This script will:
1. Start the proxy server
2. Connect to the WebSocket
3. Send a test request to the proxy
4. Monitor for intercepted messages
"""

import sys
import os
import time
import json
import asyncio
import threading
import requests
import websockets
from concurrent.futures import Thread<PERSON>oolExecutor

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.config import PROXY_HOST, PROXY_PORT, WEBSOCKET_URL

def test_proxy_health():
    """Test if the proxy server is running and healthy."""
    try:
        response = requests.get(f"http://{PROXY_HOST}:{PROXY_PORT}/health", timeout=5)
        if response.status_code == 200:
            print("✓ Proxy server is running and healthy")
            print(f"  Health response: {response.json()}")
            return True
        else:
            print(f"✗ Proxy server health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("✗ Cannot connect to proxy server - is it running?")
        return False
    except Exception as e:
        print(f"✗ Error checking proxy health: {e}")
        return False

async def test_websocket_connection():
    """Test WebSocket connection to the proxy."""
    try:
        print(f"Connecting to WebSocket at {WEBSOCKET_URL}...")
        async with websockets.connect(WEBSOCKET_URL) as websocket:
            print("✓ WebSocket connection established")
            
            # Wait for connection status message
            try:
                message = await asyncio.wait_for(websocket.recv(), timeout=5)
                data = json.loads(message)
                print(f"✓ Received connection message: {data}")
                return True
            except asyncio.TimeoutError:
                print("⚠ No connection message received within 5 seconds")
                return True  # Connection still works
            except Exception as e:
                print(f"✗ Error receiving connection message: {e}")
                return False
                
    except Exception as e:
        print(f"✗ WebSocket connection failed: {e}")
        return False

async def monitor_websocket_messages():
    """Monitor WebSocket for intercepted messages."""
    messages_received = []
    
    try:
        print(f"Starting WebSocket monitor at {WEBSOCKET_URL}...")
        async with websockets.connect(WEBSOCKET_URL) as websocket:
            print("✓ WebSocket monitor connected")
            
            # Listen for messages for 30 seconds
            start_time = time.time()
            while time.time() - start_time < 30:
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=1)
                    data = json.loads(message)
                    messages_received.append(data)
                    print(f"📨 Received message: {data.get('type', 'unknown')} - {data}")
                    
                    # If we get an intercepted request, that's what we're looking for!
                    if data.get('type') == 'intercepted_request':
                        print("🎯 INTERCEPTED REQUEST DETECTED!")
                        print(f"   Request ID: {data.get('request_id')}")
                        print(f"   Path: {data.get('path')}")
                        print(f"   Model: {data.get('model')}")
                        print(f"   Data: {json.dumps(data.get('data', {}), indent=2)}")
                        
                except asyncio.TimeoutError:
                    continue  # Keep listening
                except Exception as e:
                    print(f"Error receiving message: {e}")
                    break
                    
    except Exception as e:
        print(f"WebSocket monitor error: {e}")
    
    return messages_received

def send_test_request():
    """Send a test request to the proxy to trigger interception."""
    print("Sending test request to proxy...")
    
    test_payload = {
        "model": "gpt-3.5-turbo",
        "messages": [
            {"role": "user", "content": "Hello, this is a test message for interception!"}
        ],
        "temperature": 0.7,
        "max_tokens": 50
    }
    
    try:
        response = requests.post(
            f"http://{PROXY_HOST}:{PROXY_PORT}/v1/chat/completions",
            json=test_payload,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"✓ Test request sent, response status: {response.status_code}")
        if response.status_code == 200:
            print("✓ Request was processed successfully")
        else:
            print(f"⚠ Request failed with status {response.status_code}: {response.text}")
            
        return response.status_code == 200
        
    except Exception as e:
        print(f"✗ Error sending test request: {e}")
        return False

async def run_interception_test():
    """Run the complete interception test."""
    print("OAPIE-Lite Interception Debug Test")
    print("=" * 50)
    
    # Step 1: Check proxy health
    if not test_proxy_health():
        print("\n❌ Proxy server is not running. Please start it first.")
        return False
    
    # Step 2: Test WebSocket connection
    print("\nTesting WebSocket connection...")
    if not await test_websocket_connection():
        print("\n❌ WebSocket connection failed.")
        return False
    
    # Step 3: Start monitoring WebSocket in background
    print("\nStarting WebSocket message monitor...")
    monitor_task = asyncio.create_task(monitor_websocket_messages())
    
    # Step 4: Wait a moment for monitor to connect
    await asyncio.sleep(2)
    
    # Step 5: Send test request
    print("\nSending test request to trigger interception...")
    request_success = send_test_request()
    
    # Step 6: Wait for monitor to finish and collect results
    print("\nWaiting for intercepted messages...")
    messages = await monitor_task
    
    # Step 7: Analyze results
    print(f"\n📊 Test Results:")
    print(f"   Proxy Health: ✓")
    print(f"   WebSocket Connection: ✓")
    print(f"   Test Request Sent: {'✓' if request_success else '✗'}")
    print(f"   Messages Received: {len(messages)}")
    
    intercepted_requests = [msg for msg in messages if msg.get('type') == 'intercepted_request']
    print(f"   Intercepted Requests: {len(intercepted_requests)}")
    
    if intercepted_requests:
        print("\n🎉 SUCCESS: Interception is working!")
        for i, req in enumerate(intercepted_requests):
            print(f"   Request {i+1}: {req.get('request_id')} ({req.get('model')})")
    else:
        print("\n❌ PROBLEM: No intercepted requests detected!")
        print("\nPossible issues:")
        print("   1. WebSocket connection not established properly")
        print("   2. Proxy not sending interception messages")
        print("   3. Request not matching interception pattern")
        print("   4. Timeout too short")
        
        if messages:
            print(f"\nOther messages received:")
            for msg in messages:
                print(f"   - {msg.get('type', 'unknown')}: {msg}")
    
    return len(intercepted_requests) > 0

def main():
    """Main function to run the test."""
    try:
        # Run the async test
        result = asyncio.run(run_interception_test())
        
        if result:
            print("\n✅ Interception test PASSED!")
        else:
            print("\n❌ Interception test FAILED!")
            
        return result
        
    except KeyboardInterrupt:
        print("\n⚠ Test interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
