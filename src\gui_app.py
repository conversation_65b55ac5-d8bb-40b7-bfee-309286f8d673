"""
GUI Application Module for OAPIE-Lite.

This module implements the desktop GUI for OAPIE-Lite using CustomTkinter.
It provides a user interface for controlling the proxy server, viewing and
manipulating intercepted requests, and interacting with LLMs directly.

Key Components:
1. Proxy Control: Start/stop the proxy server
2. Live Manipulation: View and edit intercepted requests
3. Activity Log: Display status updates and events
4. Integrated Chatbox: Direct interaction with LLMs
5. WebSocket Client: Real-time communication with the proxy server

Usage:
    Run this module directly to start the GUI:
    ```
    python -m src.gui_app
    ```
"""

import customtkinter
import threading
import time
import sys
import os
import asyncio
import json
import queue
import websockets
import httpx

# Add the parent directory to the Python path
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Import configuration
from src.config import (
    WEBSOCKET_URL, GUI_PROXY_RELAY_ENDPOINT,
    WS_RECEIVE_TIMEOUT, WS_ACK_TIMEOUT, GUI_QUEUE_CHECK_INTERVAL,
    GUI_LOG_BATCH_SIZE, GUI_LOG_MAX_LINES
)

class App(customtkinter.CTk):
    """
    Main application class for the OAPIE-Lite GUI.
    
    This class implements the desktop GUI using CustomTkinter. It provides:
    - Proxy server control (start/stop)
    - WebSocket communication with the proxy
    - Live request manipulation interface
    - Activity logging
    - Integrated chatbox for direct LLM interaction
    
    The GUI is designed to be responsive and thread-safe, with all UI updates
    happening on the main thread even when triggered by background threads.
    """
    
    def __init__(self):
        super().__init__()

        self.title("Proxy Control GUI")
        self.geometry("800x600")
        
        # Set up a timer to process the WebSocket message queue
        self.after(GUI_QUEUE_CHECK_INTERVAL, self._process_gui_queue)

        # Bind window events for performance optimization
        self.bind("<Configure>", self._on_window_configure)
        self.bind("<Button-1>", self._on_user_interaction)
        self.bind("<Key>", self._on_user_interaction)

        # Configure grid layout (8 rows, 1 column)
        self.grid_rowconfigure(0, weight=0)  # Status panel
        self.grid_rowconfigure(1, weight=0)  # Start button
        self.grid_rowconfigure(2, weight=0)  # Stop button
        self.grid_rowconfigure(3, weight=1)  # Activity log
        self.grid_rowconfigure(4, weight=1)  # Live Manipulation area
        self.grid_rowconfigure(5, weight=0)  # Send Manipulated Message button
        self.grid_rowconfigure(6, weight=1)  # Chat display
        self.grid_rowconfigure(7, weight=0)  # Input area
        self.grid_columnconfigure(0, weight=1)

        # Status Panel
        self.status_frame = customtkinter.CTkFrame(self)
        self.status_frame.grid(row=0, column=0, padx=20, pady=10, sticky="ew")
        self.status_frame.grid_columnconfigure(0, weight=1)
        self.status_frame.grid_columnconfigure(1, weight=1)
        self.status_frame.grid_columnconfigure(2, weight=1)
        
        # Proxy Status
        self.proxy_status_label = customtkinter.CTkLabel(
            self.status_frame, 
            text="Proxy: Stopped",
            fg_color="#3B3B3B",
            corner_radius=6,
            padx=10,
            pady=5
        )
        self.proxy_status_label.grid(row=0, column=0, padx=5, pady=5, sticky="ew")
        
        # WebSocket Status
        self.ws_status_label = customtkinter.CTkLabel(
            self.status_frame, 
            text="WebSocket: Disconnected",
            fg_color="#3B3B3B",
            corner_radius=6,
            padx=10,
            pady=5
        )
        self.ws_status_label.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        
        # Database Status
        self.db_status_label = customtkinter.CTkLabel(
            self.status_frame, 
            text="Database: Unknown",
            fg_color="#3B3B3B",
            corner_radius=6,
            padx=10,
            pady=5
        )
        self.db_status_label.grid(row=0, column=2, padx=5, pady=5, sticky="ew")
        
        # Start Proxy Button
        self.start_button = customtkinter.CTkButton(self, text="Start Proxy", command=self.start_proxy)
        self.start_button.grid(row=1, column=0, padx=20, pady=10, sticky="ew")

        # Stop Proxy Button
        self.stop_button = customtkinter.CTkButton(self, text="Stop Proxy", command=self.stop_proxy, state="disabled")
        self.stop_button.grid(row=2, column=0, padx=20, pady=10, sticky="ew")

        # Activity Log Textbox (optimized for performance)
        self.activity_log = customtkinter.CTkTextbox(self)
        self.activity_log.grid(row=3, column=0, padx=20, pady=10, sticky="nsew")
        self.activity_log.insert("end", "Activity Log:\n")
        self.activity_log.configure(state="disabled")  # Make it read-only
        self._activity_log_lines = 1  # Initialize line counter
        
        # Live Manipulation Label and Textbox
        self.manipulation_frame = customtkinter.CTkFrame(self)
        self.manipulation_frame.grid(row=4, column=0, padx=20, pady=(10, 0), sticky="nsew")
        self.manipulation_frame.grid_columnconfigure(0, weight=1)
        self.manipulation_frame.grid_rowconfigure(0, weight=0)
        self.manipulation_frame.grid_rowconfigure(1, weight=1)
        
        self.manipulation_label = customtkinter.CTkLabel(
            self.manipulation_frame, 
            text="Live Interception & Manipulation:",
            anchor="w",
            font=("Roboto", 12, "bold")
        )
        self.manipulation_label.grid(row=0, column=0, padx=10, pady=5, sticky="w")
        
        self.manipulation_textbox = customtkinter.CTkTextbox(self.manipulation_frame)
        self.manipulation_textbox.grid(row=1, column=0, padx=10, pady=5, sticky="nsew")
        self.manipulation_textbox.insert("end", "Intercepted requests will appear here for manipulation.\n")
        self.manipulation_textbox.configure(state="disabled")  # Disabled by default
        
        # Send Manipulated Message Button
        self.send_manipulated_button = customtkinter.CTkButton(
            self, 
            text="Send Manipulated Message", 
            command=self.send_manipulated_message,
            state="disabled"  # Disabled by default
        )
        self.send_manipulated_button.grid(row=5, column=0, padx=20, pady=10, sticky="ew")

        # Chat Display Textbox (optimized for performance)
        self.chat_display = customtkinter.CTkTextbox(self)
        self.chat_display.grid(row=6, column=0, padx=20, pady=10, sticky="nsew")
        self.chat_display.insert("end", "Chat:\n")
        self.chat_display.configure(state="disabled")  # Make it read-only
        self._chat_log_lines = 1  # Initialize line counter

        # Chat Input Frame (to hold input and button)
        self.chat_input_frame = customtkinter.CTkFrame(self)
        self.chat_input_frame.grid(row=7, column=0, padx=20, pady=10, sticky="ew")
        self.chat_input_frame.grid_columnconfigure(0, weight=1)
        self.chat_input_frame.grid_columnconfigure(1, weight=0)

        self.chat_input = customtkinter.CTkEntry(self.chat_input_frame)
        self.chat_input.grid(row=0, column=0, padx=(0, 10), sticky="ew")

        self.chat_send_button = customtkinter.CTkButton(
            self.chat_input_frame,
            text="Send Chat",
            width=100,
            command=self.send_chat_message
        )
        self.chat_send_button.grid(row=0, column=1, sticky="e")

        self.proxy_thread = None
        self.proxy_running = False
        
        # WebSocket related attributes
        self.ws_thread = None
        self.ws_connected = False
        self.ws_stop_event = threading.Event()
        self.ws_message_queue = queue.Queue()
        
        # Request handling
        self.current_request_id = None
        self.pending_requests = queue.Queue()  # Queue of pending requests
        self.request_data_store = {}  # Store request data by ID

        # Performance optimization variables
        self._log_update_pending = False  # Flag to prevent multiple log updates
        self._activity_log_lines = 0  # Track number of lines in activity log
        self._chat_log_lines = 0  # Track number of lines in chat log
        self._last_queue_check = 0  # Track last queue check time for adaptive intervals
        self._updates_paused = False  # Flag to temporarily pause updates during heavy operations

    def append_to_activity_log(self, message: str) -> None:
        """
        Thread-safe method to append a message to the activity log.
        
        If called from a non-main thread, it will schedule the update on the main thread.
        
        Args:
            message: The message to append to the activity log
        """
        if threading.current_thread() is threading.main_thread():
            # Called from main thread, update directly
            self._append_to_activity_log_impl(message)
        else:
            # Called from another thread, schedule update on main thread
            self.after(0, lambda: self._append_to_activity_log_impl(message))
    
    def _append_to_activity_log_impl(self, message: str) -> None:
        """
        Implementation of activity log update, should only be called from the main thread.
        Optimized to reduce performance impact during scrolling and window resizing.

        Args:
            message: The message to append to the activity log
        """
        # Skip updates if they are temporarily paused or already pending
        if self._updates_paused or self._log_update_pending:
            return

        self._log_update_pending = True

        try:
            self.activity_log.configure(state="normal")

            # Trim log if it gets too long (performance optimization)
            if self._activity_log_lines >= GUI_LOG_MAX_LINES:
                # Remove first 100 lines to keep log manageable
                self.activity_log.delete("1.0", "101.0")
                self._activity_log_lines -= 100

            self.activity_log.insert("end", message + "\n")
            self._activity_log_lines += 1

            # Only auto-scroll if user is at the bottom (performance optimization)
            # Check if scrollbar is at the bottom before auto-scrolling
            try:
                # Get the current scroll position
                scroll_pos = self.activity_log.yview()
                # If we're near the bottom (within 5% of the end), auto-scroll
                if scroll_pos[1] > 0.95:
                    self.activity_log.see("end")
            except:
                # Fallback to always scrolling if we can't determine position
                self.activity_log.see("end")

            self.activity_log.configure(state="disabled")
        finally:
            self._log_update_pending = False

    def start_proxy(self):
        if not self.proxy_running:
            self.append_to_activity_log("Attempting to start proxy server...")
            
            # Verify environment before starting
            self._verify_environment()
            
            self.proxy_running = True
            self._update_proxy_status(True)
            self.start_button.configure(state="disabled")
            self.stop_button.configure(state="normal")
            
            # Run proxy server in a separate thread
            self.proxy_thread = threading.Thread(target=self._run_proxy_server_thread)
            self.proxy_thread.daemon = True # Allow the main program to exit even if thread is running
            self.proxy_thread.start()
            
            # Reset WebSocket stop event
            self.ws_stop_event.clear()
            
            # Start WebSocket connection in a separate thread
            self.ws_thread = threading.Thread(target=self._websocket_connect_and_listen)
            self.ws_thread.daemon = True
            self.ws_thread.start()
            
            # Schedule a health check
            self.after(5000, self._check_proxy_health)
        else:
            self.append_to_activity_log("Proxy server is already running.")
            
    def _verify_environment(self):
        """Verify that the environment is properly configured"""
        # Check if .env file exists
        if not os.path.exists(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')):
            self.append_to_activity_log("Warning: .env file not found. Using default configuration.")
            
        # Check if required modules are available
        try:
            import fastapi
            import uvicorn
            import websockets
            import httpx
        except ImportError as e:
            self.append_to_activity_log(f"Error: Required module not found: {e}")
            
    def _update_proxy_status(self, running):
        """Update the proxy status label"""
        if running:
            self.proxy_status_label.configure(
                text="Proxy: Running",
                fg_color="#1E5631"  # Dark green
            )
        else:
            self.proxy_status_label.configure(
                text="Proxy: Stopped",
                fg_color="#5E1E1E"  # Dark red
            )
    
    def _check_proxy_health(self):
        """Check if the proxy server is healthy"""
        if not self.proxy_running:
            return
            
        def check_health():
            try:
                # Try to connect to the health endpoint
                try:
                    response = httpx.get("http://localhost:8000/health", timeout=2.0)
                    if response.status_code == 200:
                        self.append_to_activity_log("Proxy server health check: OK")
                        # Update database status based on health check response
                        try:
                            data = response.json()
                            if 'database' in data:
                                db_status = data['database']
                                self.after(0, lambda: self._update_db_status(db_status == 'ok'))
                        except Exception:
                            # If we can't parse the JSON, just assume the database is OK
                            self.after(0, lambda: self._update_db_status(True))
                    else:
                        self.append_to_activity_log(f"Proxy server health check failed: {response.status_code}")
                        self.after(0, lambda: self._update_proxy_status(False))
                except httpx.ConnectError:
                    self.append_to_activity_log("Proxy server not responding to health check")
                    self.after(0, lambda: self._update_proxy_status(False))
            except Exception as e:
                self.append_to_activity_log(f"Proxy server health check failed: {str(e)}")
                self.after(0, lambda: self._update_proxy_status(False))
                
        # Run in a separate thread to avoid blocking the UI
        threading.Thread(target=check_health, daemon=True).start()
        
        # Schedule the next health check
        self.after(30000, self._check_proxy_health)  # Check every 30 seconds
        
    def _update_db_status(self, connected):
        """Update the database status label"""
        if connected:
            self.db_status_label.configure(
                text="Database: Connected",
                fg_color="#1E5631"  # Dark green
            )
        else:
            self.db_status_label.configure(
                text="Database: Disconnected",
                fg_color="#5E1E1E"  # Dark red
            )

    def _run_proxy_server_thread(self):
        try:
            # Import the function directly from the module
            from src.proxy_server import run_proxy_server as proxy_run
            
            # Pass a stop_event to the proxy server for graceful shutdown
            self.stop_event = threading.Event()
            
            # Set up a new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Call the function with proper arguments
            self.append_to_activity_log("Starting proxy server...")
            proxy_run(self.stop_event, self.append_to_activity_log)
            self.append_to_activity_log("Proxy server stopped.")
        except Exception as e:
            self.append_to_activity_log(f"Error starting proxy server: {e}")
        finally:
            self.proxy_running = False
            self._update_proxy_status(False)
            self.start_button.configure(state="normal")
            self.stop_button.configure(state="disabled")
            self.stop_event = None # Clear the event after use

    def stop_proxy(self):
        if self.proxy_running and self.stop_event:
            self.append_to_activity_log("Attempting to stop proxy server...")
            self.stop_event.set() # Signal the proxy thread to stop
            
            # Signal the WebSocket thread to stop
            if self.ws_thread and self.ws_thread.is_alive():
                self.append_to_activity_log("Closing WebSocket connection...")
                self.ws_stop_event.set()
                
            # Optionally, wait for the thread to finish if needed, but not strictly necessary for basic stop
            # self.proxy_thread.join(timeout=5) 
            # if self.proxy_thread.is_alive():
            #     self.append_to_activity_log("Proxy thread did not terminate gracefully.")
        else:
            self.append_to_activity_log("Proxy server is not running.")

    def append_to_chat_display(self, message):
        """
        Thread-safe method to append a message to the chat display.
        
        If called from a non-main thread, it will schedule the update on the main thread.
        """
        if threading.current_thread() is threading.main_thread():
            # Called from main thread, update directly
            self._append_to_chat_display_impl(message)
        else:
            # Called from another thread, schedule update on main thread
            self.after(0, lambda: self._append_to_chat_display_impl(message))
    
    def _append_to_chat_display_impl(self, message):
        """
        Implementation of chat display update, should only be called from the main thread.
        Optimized to reduce performance impact during scrolling and window resizing.
        """
        try:
            self.chat_display.configure(state="normal")

            # Trim chat log if it gets too long (performance optimization)
            if self._chat_log_lines >= GUI_LOG_MAX_LINES:
                # Remove first 100 lines to keep chat manageable
                self.chat_display.delete("1.0", "101.0")
                self._chat_log_lines -= 100

            self.chat_display.insert("end", message + "\n")
            self._chat_log_lines += 1

            # Only auto-scroll if user is at the bottom (performance optimization)
            try:
                scroll_pos = self.chat_display.yview()
                if scroll_pos[1] > 0.95:
                    self.chat_display.see("end")
            except:
                self.chat_display.see("end")

            self.chat_display.configure(state="disabled")
        except Exception as e:
            # Silently handle any display errors to prevent crashes
            pass
        
    def update_ws_connection_status(self, connected: bool) -> None:
        """
        Thread-safe method to update the WebSocket connection status.
        
        Args:
            connected: True if connected, False otherwise
        """
        if threading.current_thread() is threading.main_thread():
            # Called from main thread, update directly
            self.ws_connected = connected
            self._update_ws_status_label(connected)
        else:
            # Called from another thread, schedule update on main thread
            self.after(0, lambda: self._update_ws_connection_status_impl(connected))
    
    def _update_ws_connection_status_impl(self, connected):
        """Implementation of WebSocket connection status update"""
        self.ws_connected = connected
        self._update_ws_status_label(connected)
    
    def _update_ws_status_label(self, connected):
        """Update the WebSocket status label"""
        if connected:
            self.ws_status_label.configure(
                text="WebSocket: Connected",
                fg_color="#1E5631"  # Dark green
            )
        else:
            self.ws_status_label.configure(
                text="WebSocket: Disconnected",
                fg_color="#5E1E1E"  # Dark red
            )
    
    def _process_gui_queue(self):
        """
        Process messages from the WebSocket message queue in the main thread.
        Optimized for better performance with adaptive intervals and batch processing.
        """
        current_time = time.time()
        messages_processed = 0

        try:
            # Process messages in batches to avoid blocking the UI
            while not self.ws_message_queue.empty() and messages_processed < GUI_LOG_BATCH_SIZE:
                message = self.ws_message_queue.get_nowait()
                messages_processed += 1

                # Process different message types
                message_type = message.get('type')

                if message_type == 'intercepted_request':
                    request_id = message.get('request_id')
                    request_data = message.get('data')
                    if request_id and request_data:
                        # Store the request data
                        self.request_data_store[request_id] = request_data

                        # Add to pending requests queue
                        self.pending_requests.put(request_id)
                        self.append_to_activity_log(f"Added request {request_id} to queue (size: {self.pending_requests.qsize()})")

                        # If no request is currently being manipulated, display the next one
                        if not self.current_request_id:
                            self._process_next_request()

                elif message_type == 'forwarded_response':
                    path = message.get('data', {}).get('path')
                    status = message.get('data', {}).get('status_code')
                    model = message.get('data', {}).get('model')
                    self.append_to_activity_log(f"Response forwarded for {path}, status: {status}, model: {model}")

                elif message_type == 'gui_chat_response':
                    self.append_to_activity_log("Received chat response via WebSocket")

                elif message_type == 'connection_status':
                    status = message.get('status')
                    msg = message.get('message')
                    self.append_to_activity_log(f"WebSocket status: {status} - {msg}")

                else:
                    self.append_to_activity_log(f"Received unknown message type: {message_type}")

        except queue.Empty:
            # No more messages to process
            pass
        except Exception as e:
            self.append_to_activity_log(f"Error processing WebSocket messages: {str(e)}")

        # Adaptive scheduling: if we processed messages, check again sooner
        # If no messages, use longer interval to reduce CPU usage
        if messages_processed > 0:
            next_interval = max(50, GUI_QUEUE_CHECK_INTERVAL // 2)  # Faster when active
        else:
            next_interval = GUI_QUEUE_CHECK_INTERVAL  # Normal interval when idle

        # If there are still messages in the queue, schedule immediate processing
        if not self.ws_message_queue.empty():
            next_interval = 10  # Very fast processing when backlog exists

        self._last_queue_check = current_time
        self.after(next_interval, self._process_gui_queue)

    def _on_window_configure(self, event):
        """
        Handle window resize events to temporarily reduce update frequency.
        This helps prevent freezing during window resizing.
        """
        # Only handle events for the main window, not child widgets
        if event.widget == self:
            # Temporarily increase the queue check interval during resize
            # This will be reset by the adaptive scheduling in _process_gui_queue
            pass

    def _on_user_interaction(self, event):
        """
        Handle user interaction events to optimize performance during active use.
        """
        # During active user interaction, we can reduce some background processing
        # This is handled by the adaptive scheduling in _process_gui_queue
        pass
    
    def display_intercepted_request(self, request_id, request_data):
        """
        Display intercepted request in the manipulation textbox and enable editing.
        
        If another request is currently being manipulated, this request will be queued.
        """
        # Store the request data
        self.request_data_store[request_id] = request_data
        
        # If we're already manipulating a request, queue this one
        if self.current_request_id:
            self.pending_requests.put(request_id)
            self.append_to_activity_log(f"Request {request_id} queued for manipulation (current: {self.current_request_id})")
            return
            
        # Otherwise, display this request
        self._display_request(request_id)
    
    def _process_next_request(self):
        """
        Process the next request in the queue, if any.
        """
        if self.current_request_id:
            # Already processing a request
            return
            
        try:
            # Try to get a request from the queue (non-blocking)
            if not self.pending_requests.empty():
                next_request_id = self.pending_requests.get_nowait()
                self.append_to_activity_log(f"Processing next queued request: {next_request_id}")
                self._display_request(next_request_id)
        except queue.Empty:
            # No pending requests
            pass
        except Exception as e:
            self.append_to_activity_log(f"Error processing next request: {str(e)}")
    
    def _display_request(self, request_id):
        """
        Internal method to display a request in the manipulation textbox.
        
        Args:
            request_id: The ID of the request to display
        """
        if request_id not in self.request_data_store:
            self.append_to_activity_log(f"Error: Request {request_id} not found in data store")
            return
            
        # Store the current request ID
        self.current_request_id = request_id
        
        # Get the request data
        request_data = self.request_data_store[request_id]
        
        # Format the request data as pretty JSON
        formatted_json = json.dumps(request_data, indent=2)
        
        # Update the manipulation textbox
        self.manipulation_textbox.configure(state="normal")
        self.manipulation_textbox.delete("1.0", "end")
        self.manipulation_textbox.insert("end", formatted_json)
        
        # Enable the send button
        self.send_manipulated_button.configure(state="normal")
        
        # Log the action
        self.append_to_activity_log(f"Intercepted request {request_id} ready for manipulation")
        
    def send_manipulated_message(self):
        """Send the manipulated message back to the proxy server"""
        if not self.current_request_id:
            self.append_to_activity_log("Error: No active request to manipulate")
            return
            
        if not self.ws_connected:
            self.append_to_activity_log("Error: WebSocket not connected")
            return
            
        # Get the manipulated JSON from the textbox
        manipulated_json_str = self.manipulation_textbox.get("1.0", "end").strip()
        
        # Validate JSON before sending
        try:
            manipulated_data = json.loads(manipulated_json_str)
        except json.JSONDecodeError:
            self.append_to_activity_log("Error: Invalid JSON in manipulation textbox")
            return
            
        # Create the message to send back to the proxy
        message = {
            "type": "manipulated_message",
            "request_id": self.current_request_id,
            "data": manipulated_data
        }
        
        # Store current request ID for later cleanup
        current_id = self.current_request_id
        
        # Send the manipulated message back to the proxy via WebSocket
        def send_ws_message_thread():
            try:
                # Create or get an event loop for this thread
                try:
                    # Try to get the existing event loop
                    loop = asyncio.get_event_loop()
                except RuntimeError:
                    # If there's no event loop in the current thread, create one
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                
                async def send_message():
                    try:
                        # Ensure WebSocket URL is correct
                        ws_url = WEBSOCKET_URL
                        if not ws_url.startswith("ws://"):
                            ws_url = f"ws://localhost:8000/ws"
                            self.append_to_activity_log(f"Invalid WebSocket URL, using default: {ws_url}")
                            
                        async with websockets.connect(ws_url) as websocket:
                            await websocket.send(json.dumps(message))
                            self.append_to_activity_log(f"Sent manipulated message for request {current_id}")
                            
                            # Wait for acknowledgment
                            response = await asyncio.wait_for(websocket.recv(), timeout=WS_ACK_TIMEOUT)
                            response_data = json.loads(response)
                            if response_data.get("type") == "status" and response_data.get("status") == "received":
                                self.append_to_activity_log("Server acknowledged receipt of manipulated message")
                            else:
                                self.append_to_activity_log(f"Unexpected response: {response_data}")
                    except asyncio.TimeoutError:
                        self.append_to_activity_log("Timeout waiting for server acknowledgment")
                    except websockets.exceptions.WebSocketException as e:
                        self.append_to_activity_log(f"WebSocket error: {str(e)}")
                    except json.JSONDecodeError:
                        self.append_to_activity_log("Error: Received invalid JSON response")
                    except Exception as e:
                        self.append_to_activity_log(f"Error sending manipulated message: {str(e)}")
                
                # Run the coroutine in the event loop
                loop.run_until_complete(send_message())
                loop.close()
            except Exception as e:
                self.append_to_activity_log(f"Error setting up WebSocket for sending: {str(e)}")
        
        # Start the thread to send the message
        threading.Thread(target=send_ws_message_thread, daemon=True).start()
        
        # Reset the UI immediately (don't wait for the thread)
        self.manipulation_textbox.configure(state="normal")
        self.manipulation_textbox.delete("1.0", "end")
        self.manipulation_textbox.insert("end", "Intercepted requests will appear here for manipulation.\n")
        self.manipulation_textbox.configure(state="disabled")
        
        # Disable the send button
        self.send_manipulated_button.configure(state="disabled")
        
        # Clear the current request ID
        self.current_request_id = None
        
        # Clean up the request data
        if current_id in self.request_data_store:
            del self.request_data_store[current_id]
        
        # Check if there are any pending requests
        self.after(100, self._process_next_request)

    def send_chat_message(self):
        """Handle sending chat messages to the proxy"""
        user_input = self.chat_input.get().strip()
        if not user_input:
            return

        # Clear input field
        self.chat_input.delete(0, 'end')
        
        # Display user message
        self.append_to_chat_display(f"You: {user_input}")

        # Send request in a separate thread
        threading.Thread(target=self._send_chat_request, args=(user_input,), daemon=True).start()

    def _send_chat_request(self, user_input):
        """Send chat request to proxy server"""
        try:
            # Construct payload
            payload = {
                "model": "gpt-3.5-turbo",
                "messages": [{"role": "user", "content": user_input}]
            }

            # Ensure GUI relay endpoint is correct
            endpoint = GUI_PROXY_RELAY_ENDPOINT
            if not endpoint.startswith("http://"):
                endpoint = "http://localhost:8000/v1/gui_relay"
                self.append_to_activity_log(f"Invalid GUI relay endpoint, using default: {endpoint}")
                
            # Send request
            response = httpx.post(
                endpoint,
                json=payload,
                timeout=30.0  # Using a fixed timeout for now, could be made configurable
            )

            # Handle response
            if response.status_code == 200:
                response_data = response.json()
                reply = response_data['choices'][0]['message']['content']
                self.append_to_chat_display(f"Assistant: {reply}")
            else:
                self.append_to_chat_display(f"Error: Received status {response.status_code}")

        except Exception as e:
            self.append_to_chat_display(f"Error: {str(e)}")
    
    def _websocket_connect_and_listen(self):
        """Connect to WebSocket server and listen for messages"""
        # Set up a new asyncio event loop for this thread
        try:
            # Try to get the existing event loop
            loop = asyncio.get_event_loop()
        except RuntimeError:
            # If there's no event loop in the current thread, create one
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        # Define the WebSocket client coroutine
        async def websocket_client():
            retry_delay = 1  # Start with 1 second retry delay
            max_retry_delay = 30  # Maximum retry delay in seconds
            
            while not self.ws_stop_event.is_set():
                try:
                    # Ensure WebSocket URL is correct
                    ws_url = WEBSOCKET_URL
                    if not ws_url.startswith("ws://"):
                        ws_url = f"ws://localhost:8000/ws"
                        self.append_to_activity_log(f"Invalid WebSocket URL, using default: {ws_url}")
                    
                    self.append_to_activity_log(f"Connecting to WebSocket at {ws_url}...")
                    async with websockets.connect(ws_url) as websocket:
                        self.update_ws_connection_status(True)
                        self.append_to_activity_log("WebSocket connection established")
                        
                        # Reset retry delay on successful connection
                        retry_delay = 1
                        
                        # Listen for messages
                        while not self.ws_stop_event.is_set():
                            try:
                                # Set a timeout to periodically check if we should stop
                                message = await asyncio.wait_for(websocket.recv(), timeout=WS_RECEIVE_TIMEOUT)
                                
                                # Process the received message
                                try:
                                    data = json.loads(message)
                                    # Add the message to the queue for processing in the main thread
                                    self.ws_message_queue.put(data)
                                    # Just log that we received a message, detailed processing happens in _process_gui_queue
                                    self.append_to_activity_log(f"Received WebSocket message: {data.get('type', 'unknown')}")
                                    
                                except json.JSONDecodeError:
                                    self.append_to_activity_log(f"Received invalid JSON from WebSocket: {message[:50]}...")
                                
                            except asyncio.TimeoutError:
                                # This is expected due to the timeout we set
                                continue
                            except websockets.exceptions.ConnectionClosed:
                                self.append_to_activity_log("WebSocket connection closed")
                                break
                            except Exception as e:
                                self.append_to_activity_log(f"Error receiving WebSocket message: {str(e)}")
                                break
                    
                    # Connection closed, set status accordingly
                    self.update_ws_connection_status(False)
                    
                except (websockets.exceptions.WebSocketException, ConnectionRefusedError, Exception) as e:
                    self.update_ws_connection_status(False)
                    if not self.ws_stop_event.is_set():
                        error_type = "WebSocket connection error" if isinstance(e, (websockets.exceptions.WebSocketException, ConnectionRefusedError)) else "Unexpected WebSocket error"
                        self.append_to_activity_log(f"{error_type}: {str(e)}")
                        
                        # Check if proxy is still running
                        if not self.proxy_running:
                            self.append_to_activity_log("Proxy server appears to be stopped. Stopping WebSocket reconnection attempts.")
                            break
                            
                        self.append_to_activity_log(f"Retrying in {retry_delay} seconds...")
                        
                        # Wait for the retry delay or until stop is requested
                        try:
                            # Use wait_for with a short timeout to check stop_event frequently
                            for _ in range(int(retry_delay * 10)):  # Check every 100ms
                                if self.ws_stop_event.is_set():
                                    break
                                await asyncio.sleep(0.1)
                        except asyncio.CancelledError:
                            break
                        
                        # If stop requested during sleep, break the loop
                        if self.ws_stop_event.is_set():
                            break
                            
                        # Implement exponential backoff with jitter
                        retry_delay = min(retry_delay * 1.5, max_retry_delay)
            
            self.append_to_activity_log("WebSocket client stopped")
            self.update_ws_connection_status(False)
        
        # Run the WebSocket client in the event loop
        try:
            loop.run_until_complete(websocket_client())
        except Exception as e:
            self.append_to_activity_log(f"WebSocket thread error: {str(e)}")
        finally:
            loop.close()
            self.update_ws_connection_status(False)

if __name__ == "__main__":
    app = App()
    app.mainloop()