"""
GUI Application Module for OAPIE-Lite.

This module implements the desktop GUI for OAPIE-Lite using CustomTkinter.
It provides a user interface for controlling the proxy server, viewing and
manipulating intercepted requests, and interacting with LLMs directly.

Key Components:
1. Proxy Control: Start/stop the proxy server
2. Live Manipulation: View and edit intercepted requests
3. Activity Log: Display status updates and events
4. Integrated Chatbox: Direct interaction with LLMs
5. WebSocket Client: Real-time communication with the proxy server

Usage:
    Run this module directly to start the GUI:
    ```
    python -m src.gui_app
    ```
"""

import customtkinter
import threading
import time
import sys
import os
import asyncio
import json
import queue
import websockets
import httpx

# Add the parent directory to the Python path
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Import configuration
from src.config import (
    WEBSOCKET_URL, GUI_PROXY_RELAY_ENDPOINT,
    WS_RECEIVE_TIMEOUT, WS_ACK_TIMEOUT, GUI_QUEUE_CHECK_INTERVAL,
    GUI_LOG_BATCH_SIZE, GUI_LOG_MAX_LINES
)

# Database logger import (with fallback)
try:
    from src.db_logger import log_message_to_db
    DB_AVAILABLE = True
except ImportError:
    DB_AVAILABLE = False
    def log_message_to_db(*args, **kwargs):
        pass

# Set the appearance mode and default color theme
customtkinter.set_appearance_mode("dark")  # Modes: "System" (standard), "Dark", "Light"
customtkinter.set_default_color_theme("blue")  # Themes: "blue" (standard), "green", "dark-blue"

class App(customtkinter.CTk):
    def __init__(self):
        super().__init__()

        # Configure window
        self.title("OAPIE-Lite - LLM API Proxy & Manipulation Tool")
        self.geometry("1200x800")
        self.minsize(800, 600)

        # Configure grid layout (4x4)
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=0)  # Proxy controls
        self.grid_rowconfigure(1, weight=0)  # Status
        self.grid_rowconfigure(2, weight=0)  # Manipulation controls
        self.grid_rowconfigure(3, weight=1)  # Activity log
        self.grid_rowconfigure(4, weight=0)  # Chat controls
        self.grid_rowconfigure(5, weight=0)  # Chat input
        self.grid_rowconfigure(6, weight=1)  # Chat display

        # Proxy Control Frame
        self.proxy_frame = customtkinter.CTkFrame(self)
        self.proxy_frame.grid(row=0, column=0, padx=20, pady=10, sticky="ew")
        
        self.start_proxy_button = customtkinter.CTkButton(self.proxy_frame, text="Start Proxy", command=self.start_proxy)
        self.start_proxy_button.grid(row=0, column=0, padx=10, pady=10)
        
        self.stop_proxy_button = customtkinter.CTkButton(self.proxy_frame, text="Stop Proxy", command=self.stop_proxy, state="disabled")
        self.stop_proxy_button.grid(row=0, column=1, padx=10, pady=10)

        # Status Labels
        self.status_frame = customtkinter.CTkFrame(self)
        self.status_frame.grid(row=1, column=0, padx=20, pady=5, sticky="ew")
        
        self.proxy_status_label = customtkinter.CTkLabel(self.status_frame, text="Proxy Status: Stopped")
        self.proxy_status_label.grid(row=0, column=0, padx=10, pady=5)
        
        self.ws_status_label = customtkinter.CTkLabel(self.status_frame, text="WebSocket: Disconnected")
        self.ws_status_label.grid(row=0, column=1, padx=10, pady=5)

        # Live Interception & Manipulation Frame
        self.manipulation_frame = customtkinter.CTkFrame(self)
        self.manipulation_frame.grid(row=2, column=0, padx=20, pady=5, sticky="ew")
        
        self.manipulation_label = customtkinter.CTkLabel(self.manipulation_frame, text="Live Interception & Manipulation:")
        self.manipulation_label.grid(row=0, column=0, padx=10, pady=5, sticky="w")
        
        self.send_manipulated_button = customtkinter.CTkButton(self.manipulation_frame, text="Send Manipulated Message", 
                                                              command=self.send_manipulated_message, state="disabled")
        self.send_manipulated_button.grid(row=0, column=1, padx=10, pady=5)

        # Manipulation Textbox (put it in its own row BELOW the button frame)
        self.manipulation_textbox = customtkinter.CTkTextbox(self, height=150)
        self.manipulation_textbox.grid(row=3, column=0, padx=20, pady=10, sticky="ew")
        self.manipulation_textbox.insert("end", "Intercepted requests will appear here for manipulation...\n")

        # Activity Log Textbox (optimized for performance)
        self.activity_log = customtkinter.CTkTextbox(self)
        self.activity_log.grid(row=3, column=0, padx=20, pady=10, sticky="nsew")
        self.activity_log.insert("end", "Activity Log:\n")
        self.activity_log.configure(state="disabled")  # Make it read-only

        # Chat Control Frame
        self.chat_frame = customtkinter.CTkFrame(self)
        self.chat_frame.grid(row=4, column=0, padx=20, pady=5, sticky="ew")
        
        self.chat_label = customtkinter.CTkLabel(self.chat_frame, text="OAPIE-Lite Chatbox:")
        self.chat_label.grid(row=0, column=0, padx=10, pady=5, sticky="w")
        
        self.send_chat_button = customtkinter.CTkButton(self.chat_frame, text="Send Chat", command=self.send_chat_message)
        self.send_chat_button.grid(row=0, column=1, padx=10, pady=5)

        # Chat Input Entry
        self.chat_entry = customtkinter.CTkEntry(self, placeholder_text="Type your message here...")
        self.chat_entry.grid(row=5, column=0, padx=20, pady=5, sticky="ew")
        self.chat_entry.bind("<Return>", lambda event: self.send_chat_message())

        # Chat Display Textbox (optimized for performance)
        self.chat_display = customtkinter.CTkTextbox(self)
        self.chat_display.grid(row=6, column=0, padx=20, pady=10, sticky="nsew")
        self.chat_display.insert("end", "Chat:\n")
        self.chat_display.configure(state="disabled")  # Make it read-only

        # Initialize variables
        self.proxy_thread = None
        self.ws_thread = None
        self.ws_message_queue = queue.Queue()
        self.stop_ws_event = threading.Event()

        # Request handling
        self.current_request_id = None
        self.pending_requests = queue.Queue()  # Queue of pending requests
        self.request_data_store = {}  # Store request data by ID

        # Performance optimization variables
        self._log_update_pending = False  # Flag to prevent multiple log updates
        self._activity_log_lines = 1  # Track number of lines in activity log (start with 1 for initial message)
        self._chat_log_lines = 1  # Track number of lines in chat log (start with 1 for initial message)
        self._last_queue_check = 0  # Track last queue check time for adaptive intervals
        self._updates_paused = False  # Flag to temporarily pause updates during heavy operations

        # Set up a timer to process the WebSocket message queue
        self.after(GUI_QUEUE_CHECK_INTERVAL, self._process_gui_queue)

        # Bind window events for performance optimization
        self.bind("<Configure>", self._on_window_configure)
        self.bind("<Button-1>", self._on_user_interaction)
        self.bind("<Key>", self._on_user_interaction)

    def append_to_activity_log(self, message: str) -> None:
        """
        Thread-safe method to append a message to the activity log.
        
        Args:
            message: The message to append to the activity log
        """
        # Schedule the update to run in the main thread
        self.after(0, lambda: self._append_to_activity_log_impl(message))

    def _append_to_activity_log_impl(self, message: str) -> None:
        """
        Implementation of activity log update, should only be called from the main thread.
        Optimized to reduce performance impact during scrolling and window resizing.
        
        Args:
            message: The message to append to the activity log
        """
        # If updates are paused, queue the message for later
        if self._updates_paused:
            # Schedule the message to be processed when updates resume
            self.after(100, lambda: self._append_to_activity_log_impl(message))
            return
            
        # If already updating, schedule for immediate retry to avoid dropping messages
        if self._log_update_pending:
            self.after(10, lambda: self._append_to_activity_log_impl(message))
            return
            
        self._log_update_pending = True
        
        try:
            self.activity_log.configure(state="normal")
            
            # Trim log if it gets too long (performance optimization)
            if self._activity_log_lines >= GUI_LOG_MAX_LINES:
                # Remove first 100 lines to keep log manageable
                self.activity_log.delete("1.0", "101.0")
                self._activity_log_lines -= 100
            
            self.activity_log.insert("end", message + "\n")
            self._activity_log_lines += 1
            
            # Only auto-scroll if user is at the bottom (performance optimization)
            # Check if scrollbar is at the bottom before auto-scrolling
            try:
                # Get the current scroll position
                scroll_pos = self.activity_log.yview()
                # If we're near the bottom (within 5% of the end), auto-scroll
                if scroll_pos[1] > 0.95:
                    self.activity_log.see("end")
            except:
                # Fallback to always scrolling if we can't determine position
                self.activity_log.see("end")
                
            self.activity_log.configure(state="disabled")
        finally:
            self._log_update_pending = False

    def start_proxy(self):
        """Start the proxy server in a separate thread."""
        try:
            from src.proxy_server import run_proxy_server
            
            self.append_to_activity_log("Starting proxy server...")
            self.start_proxy_button.configure(state="disabled")
            self.proxy_status_label.configure(text="Proxy Status: Starting...")
            
            # Start proxy server in a separate thread
            self.proxy_thread = threading.Thread(
                target=run_proxy_server,
                kwargs={"log_callback": self.append_to_activity_log},
                daemon=True
            )
            self.proxy_thread.start()
            
            # Give the server a moment to start
            self.after(2000, self._check_proxy_started)
            
        except Exception as e:
            self.append_to_activity_log(f"Error starting proxy: {str(e)}")
            self.start_proxy_button.configure(state="normal")
            self.proxy_status_label.configure(text="Proxy Status: Error")

    def _check_proxy_started(self):
        """Check if the proxy server has started and update UI accordingly."""
        try:
            import requests
            response = requests.get("http://127.0.0.1:8000/health", timeout=2)
            if response.status_code == 200:
                self.append_to_activity_log("Proxy server started successfully!")
                self.proxy_status_label.configure(text="Proxy Status: Running")
                self.stop_proxy_button.configure(state="normal")
                
                # Start WebSocket connection
                self._start_websocket_client()
            else:
                self.append_to_activity_log("Proxy server health check failed")
                self._reset_proxy_ui()
        except Exception as e:
            self.append_to_activity_log(f"Failed to connect to proxy server: {str(e)}")
            self._reset_proxy_ui()

    def _reset_proxy_ui(self):
        """Reset the proxy UI to initial state."""
        self.start_proxy_button.configure(state="normal")
        self.stop_proxy_button.configure(state="disabled")
        self.proxy_status_label.configure(text="Proxy Status: Stopped")

    def stop_proxy(self):
        """Stop the proxy server and WebSocket client."""
        self.append_to_activity_log("Stopping proxy server...")
        
        # Stop WebSocket client
        self._stop_websocket_client()
        
        # Reset UI
        self._reset_proxy_ui()
        self.ws_status_label.configure(text="WebSocket: Disconnected")
        
        self.append_to_activity_log("Proxy server stopped")

    def _start_websocket_client(self):
        """Start the WebSocket client in a separate thread."""
        self.stop_ws_event.clear()
        self.ws_thread = threading.Thread(target=self._websocket_client_thread, daemon=True)
        self.ws_thread.start()

    def _stop_websocket_client(self):
        """Stop the WebSocket client."""
        self.stop_ws_event.set()
        if self.ws_thread and self.ws_thread.is_alive():
            self.ws_thread.join(timeout=2)

    def _websocket_client_thread(self):
        """WebSocket client thread function."""
        asyncio.run(self._websocket_client_async())

    async def _websocket_client_async(self):
        """Async WebSocket client implementation."""
        try:
            self.after(0, lambda: self.ws_status_label.configure(text="WebSocket: Connecting..."))
            
            async with websockets.connect(WEBSOCKET_URL) as websocket:
                self.after(0, lambda: self.ws_status_label.configure(text="WebSocket: Connected"))
                self.after(0, lambda: self.append_to_activity_log("WebSocket connected"))
                
                while not self.stop_ws_event.is_set():
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=WS_RECEIVE_TIMEOUT)
                        self.ws_message_queue.put(json.loads(message))
                    except asyncio.TimeoutError:
                        continue
                    except Exception as e:
                        self.after(0, lambda: self.append_to_activity_log(f"WebSocket error: {str(e)}"))
                        break
                        
        except Exception as e:
            self.after(0, lambda: self.append_to_activity_log(f"WebSocket connection failed: {str(e)}"))
            self.after(0, lambda: self.ws_status_label.configure(text="WebSocket: Error"))

    def send_chat_message(self):
        """Send a chat message through the GUI relay endpoint."""
        message = self.chat_entry.get().strip()
        if not message:
            return
            
        self.chat_entry.delete(0, "end")
        self.append_to_chat_display(f"You: {message}")
        
        # Send message in a separate thread to avoid blocking the UI
        threading.Thread(target=self._send_chat_async, args=(message,), daemon=True).start()

    def _send_chat_async(self, message: str):
        """Send chat message asynchronously."""
        try:
            import requests
            
            payload = {
                "model": "gpt-3.5-turbo",
                "messages": [{"role": "user", "content": message}],
                "temperature": 0.7
            }
            
            response = requests.post(GUI_PROXY_RELAY_ENDPOINT, json=payload, timeout=30)
            
            if response.status_code == 200:
                response_data = response.json()
                assistant_message = response_data.get("choices", [{}])[0].get("message", {}).get("content", "No response")
                self.append_to_chat_display(f"Assistant: {assistant_message}")
            else:
                self.append_to_chat_display(f"Error: {response.status_code} - {response.text}")
                
        except Exception as e:
            self.append_to_chat_display(f"Error sending message: {str(e)}")

    def append_to_chat_display(self, message: str) -> None:
        """Thread-safe method to append a message to the chat display."""
        self.after(0, lambda: self._append_to_chat_display_impl(message))

    def _append_to_chat_display_impl(self, message):
        """Implementation of chat display update, should only be called from the main thread."""
        try:
            self.chat_display.configure(state="normal")
            
            # Trim chat log if it gets too long (performance optimization)
            if self._chat_log_lines >= GUI_LOG_MAX_LINES:
                # Remove first 100 lines to keep chat manageable
                self.chat_display.delete("1.0", "101.0")
                self._chat_log_lines -= 100
            
            self.chat_display.insert("end", message + "\n")
            self._chat_log_lines += 1
            
            # Only auto-scroll if user is at the bottom (performance optimization)
            try:
                scroll_pos = self.chat_display.yview()
                if scroll_pos[1] > 0.95:
                    self.chat_display.see("end")
            except:
                self.chat_display.see("end")
                
            self.chat_display.configure(state="disabled")
        except Exception as e:
            # Silently handle any display errors to prevent crashes
            pass

    def send_manipulated_message(self):
        """Send the manipulated message back to the proxy server."""
        if not self.current_request_id:
            self.append_to_activity_log("No request to manipulate")
            return
            
        try:
            # Get the manipulated JSON from the textbox
            manipulated_text = self.manipulation_textbox.get("1.0", "end-1c")
            manipulated_data = json.loads(manipulated_text)
            
            # Send the manipulated message via WebSocket
            threading.Thread(target=self._send_manipulated_async, args=(manipulated_data,), daemon=True).start()
            
        except json.JSONDecodeError:
            self.append_to_activity_log("Error: Invalid JSON in manipulation textbox")
        except Exception as e:
            self.append_to_activity_log(f"Error sending manipulated message: {str(e)}")

    def _send_manipulated_async(self, manipulated_data):
        """Send manipulated message asynchronously."""
        asyncio.run(self._send_manipulated_websocket(manipulated_data))

    async def _send_manipulated_websocket(self, manipulated_data):
        """Send manipulated message via WebSocket."""
        try:
            current_id = self.current_request_id
            if not current_id:
                return
                
            message = {
                "type": "manipulated_message",
                "request_id": current_id,
                "data": manipulated_data
            }
            
            # Connect to WebSocket and send message
            ws_url = WEBSOCKET_URL
            if not ws_url.startswith(('ws://', 'wss://')):
                if ws_url.startswith('http://'):
                    ws_url = ws_url.replace('http://', 'ws://')
                elif ws_url.startswith('https://'):
                    ws_url = ws_url.replace('https://', 'wss://')
                else:
                    ws_url = f"ws://localhost:8000/ws"
                    self.append_to_activity_log(f"Invalid WebSocket URL, using default: {ws_url}")
                    
            async with websockets.connect(ws_url) as websocket:
                await websocket.send(json.dumps(message))
                self.append_to_activity_log(f"Sent manipulated message for request {current_id}")
                
                # Wait for acknowledgment
                response = await asyncio.wait_for(websocket.recv(), timeout=WS_ACK_TIMEOUT)
                response_data = json.loads(response)
                if response_data.get("type") == "status" and response_data.get("status") == "received":
                    self.append_to_activity_log("Server acknowledged receipt of manipulated message")
                else:
                    self.append_to_activity_log(f"Unexpected response: {response_data}")
        except asyncio.TimeoutError:
            self.append_to_activity_log("Timeout waiting for server acknowledgment")
        except Exception as e:
            self.append_to_activity_log(f"WebSocket error: {str(e)}")

    def _process_gui_queue(self):
        """
        Process messages from the WebSocket message queue in the main thread.
        Optimized for better performance with adaptive intervals and batch processing.
        """
        current_time = time.time()
        messages_processed = 0
        
        try:
            # Process messages in batches to avoid blocking the UI
            while not self.ws_message_queue.empty() and messages_processed < GUI_LOG_BATCH_SIZE:
                message = self.ws_message_queue.get_nowait()
                messages_processed += 1
                
                # Process different message types
                message_type = message.get('type')
                
                if message_type == 'intercepted_request':
                    request_id = message.get('request_id')
                    request_data = message.get('data')
                    if request_id and request_data:
                        # Store the request data
                        self.request_data_store[request_id] = request_data
                        
                        # Add to pending requests queue
                        self.pending_requests.put(request_id)
                        self.append_to_activity_log(f"Added request {request_id} to queue (size: {self.pending_requests.qsize()})")
                        
                        # If no request is currently being manipulated, display the next one
                        if not self.current_request_id:
                            self._process_next_request()
                
                elif message_type == 'forwarded_response':
                    path = message.get('data', {}).get('path')
                    status = message.get('data', {}).get('status_code')
                    model = message.get('data', {}).get('model')
                    self.append_to_activity_log(f"Response forwarded for {path}, status: {status}, model: {model}")
                
                elif message_type == 'gui_chat_response':
                    self.append_to_activity_log("Received chat response via WebSocket")
                
                elif message_type == 'connection_status':
                    status = message.get('status')
                    msg = message.get('message')
                    self.append_to_activity_log(f"WebSocket status: {status} - {msg}")
                
                else:
                    self.append_to_activity_log(f"Received unknown message type: {message_type}")
        
        except queue.Empty:
            # No more messages to process
            pass
        except Exception as e:
            self.append_to_activity_log(f"Error processing WebSocket messages: {str(e)}")
        
        # Adaptive scheduling: if we processed messages, check again sooner
        # If no messages, use longer interval to reduce CPU usage
        if messages_processed > 0:
            next_interval = max(50, GUI_QUEUE_CHECK_INTERVAL // 2)  # Faster when active
        else:
            next_interval = GUI_QUEUE_CHECK_INTERVAL  # Normal interval when idle
            
        # If there are still messages in the queue, schedule immediate processing
        if not self.ws_message_queue.empty():
            next_interval = 10  # Very fast processing when backlog exists
        
        self._last_queue_check = current_time
        self.after(next_interval, self._process_gui_queue)

    def _process_next_request(self):
        """Process the next request in the queue."""
        try:
            if not self.pending_requests.empty():
                request_id = self.pending_requests.get_nowait()
                request_data = self.request_data_store.get(request_id)
                
                if request_data:
                    self.current_request_id = request_id
                    
                    # Display the request in the manipulation textbox
                    self.manipulation_textbox.delete("1.0", "end")
                    self.manipulation_textbox.insert("1.0", json.dumps(request_data, indent=2))
                    
                    # Enable the send button
                    self.send_manipulated_button.configure(state="normal")
                    
                    self.append_to_activity_log(f"Displaying request {request_id} for manipulation")
                else:
                    self.append_to_activity_log(f"Request data not found for {request_id}")
        except queue.Empty:
            pass
        except Exception as e:
            self.append_to_activity_log(f"Error processing next request: {str(e)}")

    def _on_window_configure(self, event):
        """Handle window resize events to temporarily reduce update frequency."""
        # Only handle events for the main window, not child widgets
        if event.widget == self:
            # Temporarily increase the queue check interval during resize
            # This will be reset by the adaptive scheduling in _process_gui_queue
            pass

    def _on_user_interaction(self, event):
        """Handle user interaction events to optimize performance during active use."""
        # During active user interaction, we can reduce some background processing
        # This is handled by the adaptive scheduling in _process_gui_queue
        pass

if __name__ == "__main__":
    app = App()
    app.mainloop()
