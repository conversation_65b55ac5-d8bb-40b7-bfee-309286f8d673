"""
Configuration module for OAPIE-Lite.
Centralizes all configurable parameters for easy modification.
"""

import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# LLM API Configuration
LLM_API_KEY = os.getenv("OPENAI_API_KEY", "")  # Default to OpenAI, can be changed
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY", "")
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY", "")
MISTRAL_API_KEY = os.getenv("MISTRAL_API_KEY", "")

# Default to OpenAI's API URL, can be changed based on selected provider
LLM_API_BASE_URL = os.getenv("LLM_API_BASE_URL", "https://api.openai.com/v1")

# Default model for the integrated chatbox
CHAT_LLM_MODEL = os.getenv("CHAT_LLM_MODEL", "gpt-3.5-turbo")

# Proxy Server Configuration
PROXY_HOST = os.getenv("PROXY_HOST", "127.0.0.1")
PROXY_PORT = int(os.getenv("PROXY_PORT", "8000"))

# WebSocket Configuration
WS_HOST = os.getenv("WS_HOST", "127.0.0.1")
WS_PORT = int(os.getenv("WS_PORT", "8000"))
WEBSOCKET_URL = f"ws://{WS_HOST}:{WS_PORT}/ws"

# GUI Configuration
GUI_PROXY_RELAY_ENDPOINT = f"http://{PROXY_HOST}:{PROXY_PORT}/v1/gui_relay"

# Database Configuration
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = int(os.getenv("DB_PORT", "5432"))
DB_NAME = os.getenv("DB_NAME", "oapie_logs_db")
DB_USER = os.getenv("DB_USER", "postgres")
DB_PASSWORD = os.getenv("DB_PASSWORD", "postgres")

# For development/testing, option to use SQLite instead of PostgreSQL
USE_SQLITE = os.getenv("USE_SQLITE", "False").lower() in ("true", "1", "t")
SQLITE_DB_PATH = os.getenv("SQLITE_DB_PATH", "oapie_logs.db")

# Application settings
DEBUG_MODE = os.getenv("DEBUG_MODE", "False").lower() in ("true", "1", "t")
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")

# Timeout settings
GUI_MANIPULATION_TIMEOUT = int(os.getenv("GUI_MANIPULATION_TIMEOUT", "300"))  # 5 minutes
LLM_REQUEST_TIMEOUT = int(os.getenv("LLM_REQUEST_TIMEOUT", "60"))  # 1 minute
WS_RECEIVE_TIMEOUT = float(os.getenv("WS_RECEIVE_TIMEOUT", "1.0"))  # 1 second
WS_ACK_TIMEOUT = float(os.getenv("WS_ACK_TIMEOUT", "5.0"))  # 5 seconds
GUI_QUEUE_CHECK_INTERVAL = int(os.getenv("GUI_QUEUE_CHECK_INTERVAL", "250"))  # 250ms (reduced frequency)

# GUI Performance settings
GUI_LOG_BATCH_SIZE = int(os.getenv("GUI_LOG_BATCH_SIZE", "10"))  # Process max 10 log entries per update
GUI_LOG_MAX_LINES = int(os.getenv("GUI_LOG_MAX_LINES", "1000"))  # Keep max 1000 lines in log

# Resource limits
MAX_PENDING_REQUESTS = int(os.getenv("MAX_PENDING_REQUESTS", "50"))  # Maximum number of pending requests
MAX_ACTIVE_CONNECTIONS = int(os.getenv("MAX_ACTIVE_CONNECTIONS", "10"))  # Maximum number of WebSocket connections