#!/usr/bin/env python3
"""
Simple test to verify interception is working.
"""

import requests
import json
import time

def test_interception():
    print("Testing OAPIE-Lite interception...")
    
    # Test payload
    payload = {
        "model": "gpt-3.5-turbo",
        "messages": [
            {"role": "user", "content": "Hello, this is a test message!"}
        ],
        "temperature": 0.7
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer sk-test123456789"  # Fake API key for testing
    }
    
    print("Sending request to proxy...")
    print(f"URL: http://127.0.0.1:8000/v1/chat/completions")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print(f"Headers: {headers}")
    print("\nThis should appear in your GUI for manipulation...")
    print("Check your GUI now!")
    
    try:
        # Send the request - this should be intercepted
        response = requests.post(
            "http://127.0.0.1:8000/v1/chat/completions",
            json=payload,
            headers=headers,
            timeout=60  # Give time for manual manipulation
        )
        
        print(f"\nResponse received!")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
    except requests.exceptions.Timeout:
        print("\nRequest timed out - this is normal if you didn't manipulate it in the GUI")
    except Exception as e:
        print(f"\nError: {e}")

if __name__ == "__main__":
    test_interception()
