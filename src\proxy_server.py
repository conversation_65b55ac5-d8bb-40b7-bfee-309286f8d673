"""
MITM Proxy Server Module for OAPIE-Lite.

This module implements the core interception, manipulation, and relay component using FastAPI.
It intercepts API calls to LLM providers, allows for manipulation of the requests through
a GUI interface, and forwards the (potentially modified) requests to the actual LLM provider.

Key Components:
1. FastAPI application with endpoints:
   - /health: Health check endpoint
   - /v1/gui_relay: Endpoint for the integrated chatbox
   - /v1/{full_path:path}: Catchall endpoint for intercepting LLM API calls
   - /ws: WebSocket endpoint for real-time communication with the GUI

2. WebSocket communication:
   - Sends intercepted requests to the GUI
   - Receives manipulated requests from the GUI
   - Broadcasts status updates and responses

3. Request manipulation:
   - Pauses intercepted requests for GUI manipulation
   - Applies user modifications before forwarding
   - Maintains request/response correlation

4. Database integration:
   - Logs all intercepted requests and responses
   - Tracks manipulation history
   - Provides audit trail

Usage:
    Run this module directly to start the proxy server:
    ```
    python -m src.proxy_server
    ```
    
    Or import and use the run_proxy_server function:
    ```python
    from src.proxy_server import run_proxy_server
    run_proxy_server()
    ```
"""

import json
import logging
import asyncio
import threading
import uuid
from typing import Dict, Any, Optional, List, Callable
from urllib.parse import urljoin

import uvicorn
import httpx
from fastapi import FastAPI, Request, WebSocket, WebSocketDisconnect, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, Response
from pydantic import BaseModel

from src.config import (
    PROXY_HOST, PROXY_PORT, 
    LLM_API_BASE_URL, LLM_API_KEY,
    GOOGLE_API_KEY, ANTHROPIC_API_KEY, MISTRAL_API_KEY,
    CHAT_LLM_MODEL,
    GUI_MANIPULATION_TIMEOUT, LLM_REQUEST_TIMEOUT,
    MAX_PENDING_REQUESTS, MAX_ACTIVE_CONNECTIONS
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="OAPIE-Lite Proxy Server",
    description="MITM proxy for LLM API interception and manipulation",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Store active WebSocket connections
active_connections: List[WebSocket] = []

# Store pending requests awaiting manipulation
pending_requests: Dict[str, Dict[str, Any]] = {}

# Store futures for requests awaiting manipulation
request_futures: Dict[str, asyncio.Future] = {}

# Models for request validation
class ChatRequest(BaseModel):
    """Model for chat requests from the GUI"""
    model: str = CHAT_LLM_MODEL
    messages: List[Dict[str, str]]
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = None
    top_p: Optional[float] = None
    frequency_penalty: Optional[float] = None
    presence_penalty: Optional[float] = None

class ManipulatedMessage(BaseModel):
    """Model for manipulated messages from the GUI"""
    type: str
    request_id: str
    data: Dict[str, Any]

# Database logger import (with fallback)
try:
    from src.db_logger import log_message_to_db, get_db_connection
    DB_AVAILABLE = True
    logger.info("Database logging enabled")
except ImportError:
    logger.warning("Database logging not available - continuing without DB")
    DB_AVAILABLE = False
    
    def log_message_to_db(*args, **kwargs):
        """Fallback function when database is not available"""
        pass
    
    def get_db_connection():
        """Fallback function when database is not available"""
        return None

@app.get("/health")
async def health_check():
    """
    Health check endpoint to verify the proxy server is running.
    
    Returns:
        Dict: Status information including database connectivity
    """
    health_status = {"status": "ok", "service": "OAPIE-Lite Proxy"}
    
    # Check database connectivity
    if DB_AVAILABLE:
        try:
            conn = get_db_connection()
            if conn:
                conn.close()
                health_status["database"] = "ok"
            else:
                health_status["database"] = "error"
        except Exception as e:
            health_status["database"] = "error"
            health_status["database_error"] = str(e)
    else:
        health_status["database"] = "disabled"
    
    return health_status

@app.post("/v1/gui_relay")
async def gui_relay(request: ChatRequest):
    """
    Relay endpoint for the integrated chatbox in the GUI.
    
    This endpoint allows the GUI to send chat requests directly to the LLM
    without going through the interception mechanism.
    
    Args:
        request: Chat request from the GUI
        
    Returns:
        JSONResponse: LLM response in OpenAI format
    """
    try:
        logger.info(f"GUI relay request: {request.model}")
        
        # Convert to OpenAI format
        payload = {
            "model": request.model,
            "messages": request.messages,
            "temperature": request.temperature,
        }
        
        if request.max_tokens:
            payload["max_tokens"] = request.max_tokens
        if request.top_p:
            payload["top_p"] = request.top_p
        if request.frequency_penalty:
            payload["frequency_penalty"] = request.frequency_penalty
        if request.presence_penalty:
            payload["presence_penalty"] = request.presence_penalty
        
        # Forward to LLM API
        headers = {
            "Authorization": f"Bearer {LLM_API_KEY}",
            "Content-Type": "application/json"
        }
        
        async with httpx.AsyncClient(timeout=LLM_REQUEST_TIMEOUT) as client:
            response = await client.post(
                urljoin(LLM_API_BASE_URL, "chat/completions"),
                json=payload,
                headers=headers
            )
            
            if response.status_code == 200:
                response_data = response.json()
                
                # Log to database if available
                if DB_AVAILABLE:
                    try:
                        log_message_to_db(
                            request_data=payload,
                            response_data=response_data,
                            source="gui_relay",
                            model=request.model
                        )
                    except Exception as e:
                        logger.error(f"Failed to log GUI relay to database: {e}")
                
                return response_data
            else:
                logger.error(f"LLM API error: {response.status_code} - {response.text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"LLM API error: {response.text}"
                )
                
    except Exception as e:
        logger.error(f"GUI relay error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def broadcast_to_gui(message: Dict[str, Any]):
    """
    Broadcast a message to all connected GUI clients.
    
    Args:
        message: Message to broadcast
    """
    if not active_connections:
        return
        
    disconnected = []
    for connection in active_connections:
        try:
            await connection.send_text(json.dumps(message))
        except Exception as e:
            logger.warning(f"Failed to send message to WebSocket client: {e}")
            disconnected.append(connection)
    
    # Remove disconnected clients
    for connection in disconnected:
        if connection in active_connections:
            active_connections.remove(connection)

async def wait_for_manipulation(request_id: str, body: Dict[str, Any], headers: Dict[str, str], full_path: str, model: str) -> Dict[str, Any]:
    """
    Send request to GUI for manipulation and wait for response.
    
    Args:
        request_id: Unique identifier for the request
        body: Request body to be manipulated
        headers: Request headers
        full_path: API endpoint path
        model: LLM model being used
        
    Returns:
        Dict[str, Any]: The manipulated request body, or the original if no manipulation occurred
    """
    # If no active connections, return the original body immediately
    if not active_connections:
        logger.info("No active WebSocket connections, proceeding without manipulation")
        return body
        
    # Send the request to the GUI for manipulation
    logger.info(f"Sending request {request_id} to GUI for manipulation")
    message = {
        "type": "intercepted_request",
        "request_id": request_id,
        "data": body,
        "headers": {k: v for k, v in headers.items() if k.lower() not in ["authorization", "api-key"]},  # Exclude sensitive headers
        "path": f"/v1/{full_path}",
        "model": model
    }
    
    # Create a future to wait for the manipulation
    future = asyncio.Future()
    request_futures[request_id] = future
    pending_requests[request_id] = {
        "original_body": body,
        "headers": headers,
        "path": full_path,
        "model": model
    }
    
    # Broadcast to GUI
    await broadcast_to_gui(message)
    
    try:
        # Wait for manipulation with timeout
        manipulated_body = await asyncio.wait_for(future, timeout=GUI_MANIPULATION_TIMEOUT)
        logger.info(f"Received manipulation for request {request_id}")
        return manipulated_body
    except asyncio.TimeoutError:
        logger.warning(f"Manipulation timeout for request {request_id}, proceeding with original")
        return body
    finally:
        # Clean up
        if request_id in request_futures:
            del request_futures[request_id]
        if request_id in pending_requests:
            del pending_requests[request_id]

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """
    WebSocket endpoint for real-time communication with the GUI.

    This endpoint handles:
    - Connection management
    - Receiving manipulated messages from the GUI
    - Sending intercepted requests to the GUI
    """
    # Check if we've reached the maximum number of connections
    if len(active_connections) >= MAX_ACTIVE_CONNECTIONS:
        logger.warning(f"Maximum number of WebSocket connections reached ({MAX_ACTIVE_CONNECTIONS})")
        await websocket.close(code=1008, reason="Maximum number of connections reached")
        return

    await websocket.accept()
    active_connections.append(websocket)
    logger.info(f"WebSocket client connected. Active connections: {len(active_connections)}")

    # Send connection status
    await websocket.send_text(json.dumps({
        "type": "connection_status",
        "status": "connected",
        "message": "Connected to OAPIE-Lite proxy server"
    }))

    try:
        while True:
            # Receive message from GUI
            data = await websocket.receive_text()
            message = json.loads(data)

            logger.info(f"Received WebSocket message: {message.get('type', 'unknown')}")

            # Handle manipulated message
            if message.get("type") == "manipulated_message":
                request_id = message.get("request_id")
                manipulated_data = message.get("data")

                if request_id and request_id in request_futures:
                    # Resolve the future with the manipulated data
                    future = request_futures[request_id]
                    if not future.done():
                        future.set_result(manipulated_data)

                        # Send acknowledgment
                        await websocket.send_text(json.dumps({
                            "type": "status",
                            "status": "received",
                            "message": f"Manipulation received for request {request_id}"
                        }))
                else:
                    logger.warning(f"Received manipulation for unknown request: {request_id}")

            else:
                logger.warning(f"Unknown WebSocket message type: {message.get('type')}")

    except WebSocketDisconnect:
        logger.info("WebSocket client disconnected")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
    finally:
        if websocket in active_connections:
            active_connections.remove(websocket)
        logger.info(f"WebSocket client removed. Active connections: {len(active_connections)}")

@app.post("/v1/{full_path:path}")
async def intercept_llm_request(full_path: str, request: Request):
    """
    Intercept LLM API requests for manipulation.

    This is the main interception endpoint that catches all requests to /v1/*
    and allows for manipulation before forwarding to the actual LLM API.

    Args:
        full_path: The API endpoint path (e.g., "chat/completions")
        request: The incoming HTTP request

    Returns:
        Response: The LLM API response (potentially modified)
    """
    # Generate unique request ID
    request_id = str(uuid.uuid4())

    try:
        # Get request body
        body_bytes = await request.body()
        if body_bytes:
            body = json.loads(body_bytes)
        else:
            body = {}

        # Extract model information
        model = body.get("model", "unknown")

        logger.info(f"Intercepted request {request_id}: {request.method} /v1/{full_path} (model: {model})")

        # Get headers (excluding sensitive ones for logging)
        headers = dict(request.headers)

        # Wait for manipulation (if GUI is connected)
        manipulated_body = await wait_for_manipulation(
            request_id=request_id,
            body=body,
            headers=headers,
            full_path=full_path,
            model=model
        )

        # Prepare headers for forwarding - preserve original headers from client
        forward_headers = {}

        # Copy all original headers except host (which should be the target host)
        for key, value in headers.items():
            if key.lower() not in ['host']:
                forward_headers[key] = value

        # Ensure content-type is set
        if 'content-type' not in forward_headers and 'Content-Type' not in forward_headers:
            forward_headers["Content-Type"] = "application/json"

        # Forward to LLM API
        target_url = urljoin(LLM_API_BASE_URL, full_path)

        async with httpx.AsyncClient(timeout=LLM_REQUEST_TIMEOUT) as client:
            response = await client.request(
                method=request.method,
                url=target_url,
                json=manipulated_body if manipulated_body else None,
                headers=forward_headers
            )

            # Get response data
            response_data = None
            if response.headers.get("content-type", "").startswith("application/json"):
                try:
                    response_data = response.json()
                except:
                    response_data = {"error": "Invalid JSON response from LLM API"}

            # Log to database if available
            if DB_AVAILABLE:
                try:
                    log_message_to_db(
                        request_data=manipulated_body or body,
                        response_data=response_data,
                        source="intercepted",
                        model=model,
                        request_id=request_id
                    )
                except Exception as e:
                    logger.error(f"Failed to log to database: {e}")

            # Broadcast response info to GUI
            await broadcast_to_gui({
                "type": "forwarded_response",
                "request_id": request_id,
                "data": {
                    "path": f"/v1/{full_path}",
                    "status_code": response.status_code,
                    "model": model
                }
            })

            # Return the response
            return Response(
                content=response.content,
                status_code=response.status_code,
                headers=dict(response.headers)
            )

    except json.JSONDecodeError:
        logger.error(f"Invalid JSON in request {request_id}")
        raise HTTPException(status_code=400, detail="Invalid JSON")
    except Exception as e:
        logger.error(f"Error processing request {request_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

def run_proxy_server(stop_event: Optional[threading.Event] = None, log_callback: Optional[Callable] = None):
    """
    Run the proxy server.

    Args:
        stop_event: Optional threading event to signal server shutdown
        log_callback: Optional callback function for logging messages
    """
    if log_callback:
        log_callback("Initializing OAPIE-Lite proxy server...")

    # Configure logging
    if log_callback:
        # Add custom handler to forward logs to GUI
        class GUILogHandler(logging.Handler):
            def emit(self, record):
                if log_callback:
                    log_callback(self.format(record))

        gui_handler = GUILogHandler()
        gui_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(levelname)s - %(message)s')
        gui_handler.setFormatter(formatter)
        logger.addHandler(gui_handler)

    try:
        if log_callback:
            log_callback(f"Starting proxy server on {PROXY_HOST}:{PROXY_PORT}")

        # Run the server
        uvicorn.run(
            app,
            host=PROXY_HOST,
            port=PROXY_PORT,
            log_level="warning",  # Reduce uvicorn log verbosity
            access_log=False      # Disable access logs for cleaner output
        )

    except Exception as e:
        error_msg = f"Failed to start proxy server: {e}"
        logger.error(error_msg)
        if log_callback:
            log_callback(error_msg)
        raise

if __name__ == "__main__":
    run_proxy_server()
