"""
MITM Proxy Server Module for OAPIE-Lite.

This module implements the core interception, manipulation, and relay component using FastAPI.
It intercepts API calls to LLM providers, allows for manipulation of the requests through
a GUI interface, and forwards the (potentially modified) requests to the actual LLM provider.

Key Components:
1. FastAPI application with endpoints:
   - /health: Health check endpoint
   - /v1/gui_relay: Endpoint for the integrated chatbox
   - /v1/{full_path:path}: Catchall endpoint for intercepting LLM API calls
   - /ws: WebSocket endpoint for real-time communication with the GUI

2. WebSocket communication:
   - Sends intercepted requests to the GUI
   - Receives manipulated requests from the GUI
   - Broadcasts status updates and responses

3. Request handling:
   - Intercepts requests to LLM providers
   - Pauses execution to allow manipulation
   - Forwards manipulated requests to the LLM provider
   - Returns responses to the client

Usage:
    Run this module directly to start the proxy server:
    ```
    python -m src.proxy_server
    ```
    
    Or import and use the run_proxy_server function:
    ```
    from src.proxy_server import run_proxy_server
    run_proxy_server(stop_event, activity_log_callback)
    ```
"""

import json
import logging
import asyncio
import threading
import uuid
from typing import Dict, Any, Optional, List, Callable
from urllib.parse import urljoin

import uvicorn
import httpx
from fastapi import FastAPI, Request, WebSocket, WebSocketDisconnect, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, Response
from pydantic import BaseModel

from src.config import (
    PROXY_HOST, PROXY_PORT, 
    LLM_API_BASE_URL, LLM_API_KEY,
    GOOGLE_API_KEY, ANTHROPIC_API_KEY, MISTRAL_API_KEY,
    CHAT_LLM_MODEL,
    GUI_MANIPULATION_TIMEOUT, LLM_REQUEST_TIMEOUT,
    MAX_PENDING_REQUESTS, MAX_ACTIVE_CONNECTIONS
)
from src.db_logger import log_message_to_db, extract_info_from_request, extract_info_from_response

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(title="OAPIE-Lite Proxy Server")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development; restrict in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Store active WebSocket connections
active_connections: List[WebSocket] = []

# Store pending requests awaiting manipulation
pending_requests: Dict[str, Dict[str, Any]] = {}

# Store futures for requests awaiting manipulation
request_futures: Dict[str, asyncio.Future] = {}

# Models for request validation
class ChatRequest(BaseModel):
    """Model for chat requests from the GUI"""
    model: str = CHAT_LLM_MODEL
    messages: List[Dict[str, str]]
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = None
    top_p: Optional[float] = None
    frequency_penalty: Optional[float] = None
    presence_penalty: Optional[float] = None

class WebSocketMessage(BaseModel):
    """Model for messages received via WebSocket"""
    type: str
    request_id: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
    
    class Config:
        """Pydantic model configuration"""
        extra = "allow"  # Allow extra fields for forward compatibility

@app.get("/health")
async def health_check():
    """
    Health check endpoint
    
    Returns status information about the proxy server and its components
    """
    # Check database connection
    db_status = "unknown"
    try:
        try:
            from src.db_logger import get_db_connection
            conn = get_db_connection(retry_count=0)  # Don't retry in health check
            if conn:
                # Try a simple query
                if hasattr(conn, 'cursor'):  # PostgreSQL
                    cursor = conn.cursor()
                    try:
                        cursor.execute("SELECT 1")
                        db_status = "ok"
                    finally:
                        cursor.close()
                else:  # SQLite
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    db_status = "ok"
                conn.close()
        except ImportError:
            logger.warning("Could not import db_logger module")
            db_status = "unknown"
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            db_status = "error"
    except Exception as e:
        logger.error(f"Unexpected error in health check: {e}")
        db_status = "error"
    
    try:
        return {
            "status": "ok",
            "database": db_status,
            "active_connections": len(active_connections),
            "pending_requests": len(request_futures)
        }
    except Exception as e:
        logger.error(f"Error creating health check response: {e}")
        return {"status": "ok"}

@app.post("/v1/gui_relay")
async def gui_relay(request: ChatRequest):
    """
    Endpoint for the GUI's integrated chatbox to send messages to the LLM API.
    
    This endpoint receives structured LLM requests from the GUI, forwards them to the
    target LLM API, and returns the response.
    """
    try:
        # Log the incoming request
        logger.info(f"Received GUI chat request: {request.model}")
        
        # Extract info for logging
        model = request.model
        messages_text = "\n".join([f"{m.get('role', 'unknown')}: {m.get('content', '')}" for m in request.messages])
        estimated_tokens = len(messages_text) // 4  # Rough estimate
        
        # Log to database
        log_message_to_db(
            direction="request",
            message_type="gui_chat",
            data=request.model_dump(),
            path="/v1/gui_relay",
            model=model,
            prompt=messages_text,
            tokens=estimated_tokens
        )
        
        # For now, just return a placeholder response
        # In a future task, this will actually forward the request to the LLM API
        response_data = {
            "id": "placeholder-response-id",
            "object": "chat.completion",
            "created": 1677858242,
            "model": request.model,
            "choices": [
                {
                    "message": {
                        "role": "assistant",
                        "content": "This is a placeholder response from the OAPIE-Lite proxy server. The actual LLM integration will be implemented in a future task."
                    },
                    "finish_reason": "stop",
                    "index": 0
                }
            ],
            "usage": {
                "prompt_tokens": estimated_tokens,
                "completion_tokens": 30,
                "total_tokens": estimated_tokens + 30
            }
        }
        
        # Log the response
        log_message_to_db(
            direction="response",
            message_type="gui_chat",
            data=response_data,
            path="/v1/gui_relay",
            model=model,
            response=response_data["choices"][0]["message"]["content"],
            tokens=response_data["usage"]["total_tokens"],
            status_code=200
        )
        
        # Broadcast to WebSocket clients if any are connected
        if active_connections:
            message = {
                "type": "gui_chat_response",
                "request_id": "gui-chat-" + str(len(pending_requests)),
                "data": response_data
            }
            await broadcast_message(message)
        
        return response_data
        
    except Exception as e:
        logger.error(f"Error in GUI relay: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """
    WebSocket endpoint for real-time communication with the GUI.
    
    This endpoint handles:
    - Connection management
    - Receiving manipulated messages from the GUI
    - Sending intercepted requests to the GUI
    """
    # Check if we've reached the maximum number of connections
    if len(active_connections) >= MAX_ACTIVE_CONNECTIONS:
        logger.warning(f"Maximum number of WebSocket connections reached ({MAX_ACTIVE_CONNECTIONS})")
        await websocket.close(code=1008, reason="Maximum number of connections reached")
        return
        
    await websocket.accept()
    active_connections.append(websocket)
    logger.info(f"WebSocket client connected. Active connections: {len(active_connections)}")
    
    try:
        # Send a welcome message
        await websocket.send_json({
            "type": "connection_status",
            "status": "connected",
            "message": "Connected to OAPIE-Lite proxy server"
        })
        
        # Listen for messages from the client
        while True:
            data = await websocket.receive_text()
            try:
                # Parse and validate the message using the Pydantic model
                try:
                    ws_message = WebSocketMessage.model_validate_json(data)
                    logger.debug(f"Received WebSocket message: {ws_message.model_dump()}")
                    message = ws_message.model_dump()
                except Exception as e:
                    logger.error(f"Invalid WebSocket message format: {e}")
                    await websocket.send_json({
                        "type": "error",
                        "message": f"Invalid message format: {str(e)}"
                    })
                    continue
                
                # Handle different message types
                if ws_message.type == "manipulated_message" and ws_message.request_id:
                    await handle_manipulated_message(websocket, ws_message, message)
                        
            except json.JSONDecodeError:
                logger.error(f"Invalid JSON received: {data}")
                await websocket.send_json({
                    "type": "error",
                    "message": "Invalid JSON format"
                })
                
    except WebSocketDisconnect:
        logger.info("WebSocket client disconnected")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
    finally:
        # Remove the connection from active_connections
        if websocket in active_connections:
            active_connections.remove(websocket)
        logger.info(f"WebSocket connection closed. Active connections: {len(active_connections)}")

def cleanup_request_resources(request_id: str):
    """
    Clean up resources associated with a request.
    
    This function removes the request's future and context from the global dictionaries,
    preventing memory leaks and ensuring proper cleanup.
    
    Args:
        request_id: The ID of the request to clean up
    """
    if request_id in request_futures:
        del request_futures[request_id]
    if request_id in pending_requests:
        del pending_requests[request_id]

async def intercept_and_manipulate_request(
    request_id: str,
    body: Dict[str, Any],
    headers: Dict[str, str],
    full_path: str,
    model: str
) -> Dict[str, Any]:
    """
    Send a request to the GUI for manipulation and wait for the response.
    
    This function broadcasts the request to all connected GUI clients,
    then waits for a response with the manipulated request body.
    
    Args:
        request_id: The unique ID of the request
        body: The original request body
        headers: The request headers
        full_path: The API endpoint path
        model: The LLM model name
        
    Returns:
        Dict[str, Any]: The manipulated request body, or the original if no manipulation occurred
    """
    # If no active connections, return the original body immediately
    if not active_connections:
        logger.info("No active WebSocket connections, proceeding without manipulation")
        return body
        
    # Send the request to the GUI for manipulation
    logger.info(f"Sending request {request_id} to GUI for manipulation")
    message = {
        "type": "intercepted_request",
        "request_id": request_id,
        "data": body,
        "headers": {k: v for k, v in headers.items() if k.lower() not in ["authorization", "api-key"]},  # Exclude sensitive headers
        "path": f"/v1/{full_path}",
        "model": model
    }
    await broadcast_message(message)
    
    # Get the future for this request
    future = request_futures.get(request_id)
    if not future:
        logger.error(f"Future for request {request_id} not found")
        return body
        
    try:
        # Wait for the GUI to send back the manipulated request
        # Use a timeout to prevent hanging indefinitely
        manipulated_body = await asyncio.wait_for(future, timeout=GUI_MANIPULATION_TIMEOUT)
        logger.info(f"Received manipulated request for {request_id}")
        return manipulated_body
    except asyncio.TimeoutError:
        logger.warning(f"Timeout waiting for manipulation of request {request_id}, proceeding with original")
        return body
    except Exception as e:
        logger.error(f"Error waiting for manipulation of request {request_id}: {e}, proceeding with original")
        return body

async def handle_manipulated_message(websocket: WebSocket, ws_message: WebSocketMessage, message: Dict[str, Any]):
    """
    Handle a manipulated message from the GUI.
    
    This function processes a message with type 'manipulated_message', which contains
    a modified request body that should be used to continue processing the original request.
    
    Args:
        websocket: The WebSocket connection to respond on
        ws_message: The parsed WebSocketMessage object
        message: The raw message dictionary
    """
    request_id = message["request_id"]
    
    # Use a more atomic approach to check and set the future
    future = request_futures.get(request_id)
    if future is not None and not future.done():
        logger.info(f"Received manipulated message for request {request_id}")
        
        # Get the manipulated data
        manipulated_data = message.get("data", {})
        
        try:
            # Set the result in the future to resume processing
            future.set_result(manipulated_data)
        except asyncio.InvalidStateError:
            # Future might have been completed or cancelled between our check and set
            logger.warning(f"Future for request {request_id} is no longer valid")
            await websocket.send_json({
                "type": "error",
                "message": f"Request {request_id} is no longer valid or has timed out"
            })
            return
        
        # Acknowledge receipt
        await websocket.send_json({
            "type": "status",
            "status": "received",
            "message": f"Manipulated message for request {request_id} received"
        })
    else:
        logger.warning(f"Received manipulated message for unknown request {request_id}")
        await websocket.send_json({
            "type": "error",
            "message": f"Unknown request ID: {request_id}"
        })

async def broadcast_message(message: Dict[str, Any]):
    """
    Broadcast a message to all active WebSocket connections.
    
    This function sends a message to all connected WebSocket clients.
    It handles disconnections gracefully and removes disconnected clients
    from the active connections list.
    
    Args:
        message: The message to broadcast
    
    Returns:
        int: Number of clients the message was successfully sent to
    """
    disconnected = []
    success_count = 0
    
    # Make a copy of the list to avoid modification during iteration
    connections = list(active_connections)
    
    for connection in connections:
        try:
            await connection.send_json(message)
            success_count += 1
        except Exception as e:
            logger.error(f"Error sending message to WebSocket client: {e}")
            disconnected.append(connection)
    
    # Remove disconnected clients
    for connection in disconnected:
        if connection in active_connections:
            active_connections.remove(connection)
            
    return success_count

def verify_environment():
    """
    Verify that the environment is properly configured.
    
    Returns:
        tuple: (is_valid, message)
    """
    issues = []
    
    # Check API keys
    if not any([LLM_API_KEY, GOOGLE_API_KEY, ANTHROPIC_API_KEY, MISTRAL_API_KEY]):
        issues.append("No API keys configured. At least one provider API key is required.")
    
    # Check port availability
    import socket
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((PROXY_HOST, PROXY_PORT))
        if result == 0:
            issues.append(f"Port {PROXY_PORT} is already in use. Choose a different port.")
        sock.close()
    except Exception as e:
        issues.append(f"Error checking port availability: {e}")
    
    if issues:
        return False, "\n".join(issues)
    return True, "Environment verification passed"

def run_proxy_server(stop_event: Optional[threading.Event] = None, activity_log_callback: Optional[Callable[[str], None]] = None):
    """
    Start the proxy server using Uvicorn.
    
    This function is designed to be called from the main application or run in a separate thread.
    It now accepts a stop_event for graceful shutdown and a callback for logging activity.
    """
    # Verify environment
    is_valid, message = verify_environment()
    if not is_valid and activity_log_callback:
        activity_log_callback(f"Environment verification failed: {message}")
        # Continue anyway, but log the issues
    
    log_message = f"Starting OAPIE-Lite proxy server on {PROXY_HOST}:{PROXY_PORT}"
    logger.info(log_message)
    if activity_log_callback:
        activity_log_callback(log_message)

    config = uvicorn.Config(app, host=PROXY_HOST, port=PROXY_PORT, log_level="info")
    server = uvicorn.Server(config)

    # Get or create an event loop
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        # If there's no event loop in the current thread, create one
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

    if stop_event:
        # Function to run in a separate thread to check for stop_event
        async def shutdown_monitor():
            while not stop_event.is_set():
                await asyncio.sleep(0.1) # Check every 100ms
            logger.info("Stop event received. Shutting down proxy server...")
            if activity_log_callback:
                activity_log_callback("Stop event received. Shutting down proxy server...")
            server.should_exit = True

        # Start the shutdown monitor in a separate task
        if loop.is_running():
            # If we're in an existing event loop, create a task
            asyncio.create_task(shutdown_monitor())
        else:
            # Schedule the task to run when the loop starts
            loop.create_task(shutdown_monitor())
    
    # Run the server
    try:
        loop.run_until_complete(server.serve())
    except RuntimeError as e:
        if "This event loop is already running" in str(e):
            # If the loop is already running (which shouldn't happen),
            # try with asyncio.run instead
            logger.warning("Event loop already running, trying with asyncio.run")
            asyncio.run(server.serve())
        else:
            raise

    log_message = "Proxy server has shut down."
    logger.info(log_message)
    if activity_log_callback:
        activity_log_callback(log_message)

@app.api_route("/{full_path:path}", methods=["POST"])
async def catchall(full_path: str, request: Request):
    """
    Catchall endpoint for intercepting LLM API requests.
    
    This endpoint:
    1. Intercepts incoming requests
    2. Generates a unique request_id
    3. Extracts request data
    4. Logs the request
    5. Sends the request to the GUI for manipulation via WebSocket
    6. Pauses execution awaiting manipulation from the GUI
    7. Injects the API key
    8. Forwards the manipulated request to the LLM provider
    9. Receives the response
    10. Logs the response
    11. Returns the response to the client
    """
    request_id = str(uuid.uuid4())
    
    try:
        # Extract request data
        body = await request.json()
        headers = dict(request.headers)

        # Get the full URL with query parameters
        full_url_with_query = str(request.url).replace(str(request.base_url), "")
        if full_url_with_query.startswith("/"):
            full_url_with_query = full_url_with_query[1:]  # Remove leading slash

        # Extract info for logging
        model, prompt, estimated_tokens = extract_info_from_request(full_path, headers, body)

        # Log the incoming request
        logger.info(f"Intercepted request to /{full_url_with_query} (ID: {request_id})")
        log_message_to_db(
            direction="request",
            message_type="intercepted",
            data=body,
            path=f"/{full_url_with_query}",
            model=model,
            prompt=prompt,
            tokens=estimated_tokens
        )

        # Store request context
        request_context = {
            "full_path": full_path,
            "full_url_with_query": full_url_with_query,
            "headers": headers,
            "body": body,
            "model": model,
            "target_url": urljoin(LLM_API_BASE_URL, f"/{full_url_with_query}")
        }
        pending_requests[request_id] = request_context
        
        # Check if we've reached the maximum number of pending requests
        if len(request_futures) >= MAX_PENDING_REQUESTS:
            logger.warning(f"Maximum number of pending requests reached ({MAX_PENDING_REQUESTS})")
            return JSONResponse(
                status_code=503,
                content={"error": "Server is busy. Too many pending requests."}
            )
            
        # Create a future to await manipulation
        future = asyncio.Future()
        request_futures[request_id] = future
        
        # Send the request to the GUI for manipulation
        body = await intercept_and_manipulate_request(request_id, body, headers, full_path, model)
        
        # Prepare headers for forwarding
        forward_headers = {
            "Content-Type": "application/json",
        }
        
        # Inject appropriate API key based on the target service
        if "openai" in LLM_API_BASE_URL:
            forward_headers["Authorization"] = f"Bearer {LLM_API_KEY}"
        elif "googleapis" in LLM_API_BASE_URL:
            forward_headers["x-goog-api-key"] = GOOGLE_API_KEY
        elif "anthropic" in LLM_API_BASE_URL:
            forward_headers["x-api-key"] = ANTHROPIC_API_KEY
        elif "mistral" in LLM_API_BASE_URL:
            forward_headers["Authorization"] = f"Bearer {MISTRAL_API_KEY}"
        else:
            # Default to OpenAI-style authorization
            forward_headers["Authorization"] = f"Bearer {LLM_API_KEY}"
        
        # Construct target URL using the full URL with query parameters
        target_url = urljoin(LLM_API_BASE_URL, f"/{full_url_with_query}")
        
        # Forward the request to the LLM provider
        logger.info(f"Forwarding request to {target_url}")
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    target_url,
                    json=body,
                    headers=forward_headers,
                    timeout=LLM_REQUEST_TIMEOUT
                )
        except httpx.ConnectError:
            logger.error(f"Connection error when forwarding to {target_url}")
            return JSONResponse(
                status_code=502,
                content={"error": "Unable to connect to LLM provider"}
            )
        except httpx.TimeoutException:
            logger.error(f"Timeout when forwarding to {target_url}")
            return JSONResponse(
                status_code=504,
                content={"error": "Request to LLM provider timed out"}
            )
        except httpx.HTTPError as e:
            logger.error(f"HTTP error when forwarding to {target_url}: {e}")
            return JSONResponse(
                status_code=502,
                content={"error": f"Error communicating with LLM provider: {str(e)}"}
            )

        # Get response data
        response_status = response.status_code
        response_content = response.content

        try:
            response_body = response.json()
        except json.JSONDecodeError:
            logger.warning(f"Received non-JSON response from LLM provider: {response_content[:100]}...")

            # Try to create a structured response from the raw content
            try:
                text_content = response_content.decode('utf-8')
                response_body = {
                    "error": "Invalid JSON response from provider",
                    "raw_content": text_content[:1000]  # Limit size for safety
                }
            except UnicodeDecodeError:
                response_body = {
                    "error": "Invalid JSON response from provider (binary content)"
                }

        # Extract info for logging
        response_text, tokens = extract_info_from_response(
            full_url_with_query, dict(response.headers), response_body, response_status
        )

        # Log the response
        logger.info(f"Received response from LLM provider (ID: {request_id}, Status: {response_status})")
        log_message_to_db(
            direction="response",
            message_type="forwarded",
            data=response_body,
            path=f"/{full_url_with_query}",
            model=model,
            response=response_text,
            tokens=tokens,
            status_code=response_status
        )

        # Broadcast to WebSocket clients if any are connected
        if active_connections:
            message = {
                "type": "forwarded_response",
                "request_id": request_id,
                "data": {
                    "path": f"/{full_url_with_query}",
                    "status_code": response_status,
                    "model": model,
                    "response_preview": response_text[:100] if response_text else None
                }
            }
            await broadcast_message(message)

        # Clean up request data
        cleanup_request_resources(request_id)

        # Return the response to the client
        return Response(
            content=response.content,
            status_code=response_status,
            headers=dict(response.headers)
        )
            
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON in request body (ID: {request_id})")
        # Clean up any resources that might have been created
        cleanup_request_resources(request_id)
        return JSONResponse(
            status_code=400,
            content={"error": "Invalid JSON in request body"}
        )
    except Exception as e:
        logger.error(f"Error processing request (ID: {request_id}): {e}")
        # Clean up any resources that might have been created
        cleanup_request_resources(request_id)
        return JSONResponse(
            status_code=500,
            content={"error": f"Internal server error: {str(e)}"}
        )

if __name__ == "__main__":
    # If run directly, start the server without stop_event or callback
    run_proxy_server(None, None)