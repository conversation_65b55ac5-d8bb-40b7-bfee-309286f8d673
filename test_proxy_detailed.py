#!/usr/bin/env python3
"""
Test script to run the proxy server with detailed error logging to see what's causing the 500 errors.
"""

import sys
import os
import logging
import asyncio
import uvicorn
from contextlib import asynccontextmanager

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_config():
    """Test the configuration to see if there are any issues."""
    print("Configuration Test")
    print("=" * 30)
    
    try:
        from src.config import (
            LLM_API_KEY, LLM_API_BASE_URL, CHAT_LLM_MODEL,
            PROXY_HOST, PROXY_PORT, WEBSOCKET_URL
        )
        
        print(f"LLM_API_KEY: {'SET' if LLM_API_KEY else 'NOT SET'}")
        print(f"LLM_API_BASE_URL: {LLM_API_BASE_URL}")
        print(f"CHAT_LLM_MODEL: {CHAT_LLM_MODEL}")
        print(f"PROXY_HOST: {PROXY_HOST}")
        print(f"PROXY_PORT: {PROXY_PORT}")
        print(f"WEBSOCKET_URL: {WEBSOCKET_URL}")
        
        if not LLM_API_KEY:
            print("\n⚠ WARNING: LLM_API_KEY is not set!")
            print("This will cause 500 errors when trying to forward requests to the LLM API.")
            print("Set OPENAI_API_KEY in your .env file or environment variables.")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_proxy_server_startup():
    """Test starting the proxy server with detailed logging."""
    print("\nProxy Server Startup Test")
    print("=" * 30)
    
    try:
        from src.proxy_server import app
        print("✓ Successfully imported proxy server app")
        
        # Test a simple request handler
        print("Testing request handling...")
        
        # Create a test client
        from fastapi.testclient import TestClient
        client = TestClient(app)
        
        # Test health endpoint
        print("Testing /health...")
        response = client.get("/health")
        print(f"Health response: {response.status_code} - {response.json()}")
        
        # Test GUI relay with minimal payload
        print("Testing /v1/gui_relay...")
        test_payload = {
            "model": "gpt-3.5-turbo",
            "messages": [{"role": "user", "content": "test"}]
        }
        
        try:
            response = client.post("/v1/gui_relay", json=test_payload)
            print(f"GUI relay response: {response.status_code}")
            if response.status_code != 200:
                print(f"Error details: {response.text}")
        except Exception as e:
            print(f"GUI relay error: {e}")
            import traceback
            traceback.print_exc()
        
        # Test chat completions endpoint
        print("Testing /v1/chat/completions...")
        try:
            response = client.post("/v1/chat/completions", json=test_payload)
            print(f"Chat completions response: {response.status_code}")
            if response.status_code != 200:
                print(f"Error details: {response.text}")
        except Exception as e:
            print(f"Chat completions error: {e}")
            import traceback
            traceback.print_exc()
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing proxy server: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_env_file():
    """Check if .env file exists and what's in it."""
    print("\nEnvironment File Check")
    print("=" * 30)
    
    env_path = ".env"
    if os.path.exists(env_path):
        print(f"✓ Found .env file at {env_path}")
        try:
            with open(env_path, 'r') as f:
                content = f.read()
                lines = content.strip().split('\n')
                print(f"✓ .env file has {len(lines)} lines")
                
                # Check for API keys (without revealing them)
                for line in lines:
                    if line.strip() and not line.startswith('#'):
                        key = line.split('=')[0].strip()
                        if 'API_KEY' in key:
                            value = line.split('=', 1)[1].strip() if '=' in line else ''
                            status = "SET" if value and value != "your_api_key_here" else "NOT SET"
                            print(f"   {key}: {status}")
                        elif key in ['LLM_API_BASE_URL', 'CHAT_LLM_MODEL']:
                            value = line.split('=', 1)[1].strip() if '=' in line else ''
                            print(f"   {key}: {value}")
                            
        except Exception as e:
            print(f"❌ Error reading .env file: {e}")
    else:
        print(f"❌ No .env file found at {env_path}")
        print("Creating a default .env file...")
        
        default_env = """# LLM API Keys
OPENAI_API_KEY=your_openai_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
MISTRAL_API_KEY=your_mistral_api_key_here

# LLM Configuration
LLM_API_BASE_URL=https://api.openai.com/v1
CHAT_LLM_MODEL=gpt-3.5-turbo

# Proxy Server Configuration
PROXY_HOST=127.0.0.1
PROXY_PORT=8000

# WebSocket Configuration
WS_HOST=127.0.0.1
WS_PORT=8000

# Database Configuration
USE_SQLITE=True
SQLITE_DB_PATH=oapie_logs.db
DEBUG_MODE=True
LOG_LEVEL=INFO
"""
        
        try:
            with open(env_path, 'w') as f:
                f.write(default_env)
            print(f"✓ Created default .env file at {env_path}")
            print("⚠ Please edit the .env file and set your API keys!")
        except Exception as e:
            print(f"❌ Error creating .env file: {e}")

def main():
    """Main function."""
    print("OAPIE-Lite Proxy Server Detailed Debug")
    print("=" * 50)
    
    # Check environment file
    check_env_file()
    
    # Test configuration
    config_ok = test_config()
    
    # Test proxy server
    server_ok = test_proxy_server_startup()
    
    print("\n" + "=" * 50)
    print("DEBUG SUMMARY")
    print("=" * 50)
    print(f"Configuration: {'✓' if config_ok else '❌'}")
    print(f"Server Startup: {'✓' if server_ok else '❌'}")
    
    if not config_ok:
        print("\n🔧 TO FIX:")
        print("1. Set your OPENAI_API_KEY in the .env file")
        print("2. Make sure the .env file is in the project root")
        print("3. Restart the proxy server")
    
    return config_ok and server_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
